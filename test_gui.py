#!/usr/bin/env python3
"""
Test script for WIDDX Speed Up GUI
This script runs the application without admin privileges for testing purposes.
"""

import sys
from PySide6.QtWidgets import QApplication
from gui import MainWindow
from system_optimizer import SystemOptimizer

def main():
    """Main function to run the GUI application"""
    try:
        print("Starting WIDDX Speed Up...")
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("WIDDX Speed Up")
        app.setApplicationVersion("2.0")
        
        # Create optimizer and main window
        print("Initializing system optimizer...")
        optimizer = SystemOptimizer()
        
        print("Creating main window...")
        window = MainWindow(optimizer)
        
        # Show the window
        print("Showing main window...")
        window.show()
        
        print("WIDDX Speed Up is now running!")
        print("Note: Some features may require administrator privileges.")
        
        # Start the event loop
        return app.exec()
        
    except ImportError as e:
        print(f"Import Error: {e}")
        print("Please make sure PySide6 is installed: pip install PySide6")
        return 1
        
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
