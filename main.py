import sys
import ctypes
from PySide6.QtWidgets import QApplication
from gui import MainWindow
from system_optimizer import SystemOptimizer

def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

if not is_admin():
    # Re-run with admin privileges if needed
    ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, " ".join(sys.argv), None, 1)
    sys.exit()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    optimizer = SystemOptimizer()
    window = MainWindow(optimizer)
    window.show()
    sys.exit(app.exec())