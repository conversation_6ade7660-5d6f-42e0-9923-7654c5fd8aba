import sys
import ctypes
from PySide6.QtWidgets import <PERSON>Application, QMessageBox
from gui import MainWindow
from system_optimizer import SystemOptimizer

def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def main():
    try:
        app = QApplication(sys.argv)

        # Check for admin privileges but don't force it for testing
        if not is_admin():
            print("Warning: Running without administrator privileges. Some features may not work properly.")

        optimizer = SystemOptimizer()
        window = MainWindow(optimizer)
        window.show()

        return app.exec()

    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())