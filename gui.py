from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QTabWidget, QProgressBar, QGroupBox, QStackedWidget,
    QComboBox, QCheckBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QAbstractItemView
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QIcon, QFont, QPalette, QColor
import psutil
import os

class MainWindow(QMainWindow):
    configChanged = Signal()
    
    def __init__(self, optimizer):
        super().__init__()
        self.optimizer = optimizer
        self.setMinimumSize(800, 600)
        self.update_title()
        
        # Central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Apply theme
        self.apply_theme()
        
        # Connect config change signal
        self.configChanged.connect(self.on_config_changed)
        
        # Header with system stats
        header = QWidget()
        header_layout = QHBoxLayout(header)
        
        self.cpu_label = self.create_stat_widget(self.tr("cpu"), "0%")
        self.ram_label = self.create_stat_widget(self.tr("ram"), "0%")
        self.disk_label = self.create_stat_widget(self.tr("disk"), "0%")
        self.network_label = self.create_stat_widget(self.tr("network"), "0 KB/s")
        self.temp_label = self.create_stat_widget(self.tr("temperature"), "N/A")

        header_layout.addWidget(self.cpu_label)
        header_layout.addWidget(self.ram_label)
        header_layout.addWidget(self.disk_label)
        header_layout.addWidget(self.network_label)
        header_layout.addWidget(self.temp_label)
        header_layout.addStretch()

        # Initialize network stats for calculation
        self.last_network_stats = psutil.net_io_counters()
        
        # Optimization button
        self.optimize_btn = QPushButton(self.tr("optimize_now"))
        self.optimize_btn.setFixedHeight(40)
        self.optimize_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.optimize_btn.clicked.connect(self.run_optimization)
        
        # Tab-based interface
        self.tabs = QTabWidget()
        self.health_tab = self.create_health_tab()
        self.cleaner_tab = self.create_cleaner_tab()
        self.startup_tab = self.create_startup_tab()
        self.performance_tab = self.create_performance_tab()
        self.processes_tab = self.create_processes_tab()
        self.gaming_tab = self.create_gaming_tab()
        self.scheduler_tab = self.create_scheduler_tab()
        self.settings_tab = self.create_settings_tab()

        self.tabs.addTab(self.health_tab, self.tr("system_health"))
        self.tabs.addTab(self.cleaner_tab, self.tr("cleaner"))
        self.tabs.addTab(self.startup_tab, self.tr("startup"))
        self.tabs.addTab(self.performance_tab, self.tr("performance"))
        self.tabs.addTab(self.processes_tab, self.tr("processes"))
        self.tabs.addTab(self.gaming_tab, self.tr("gaming_mode"))
        self.tabs.addTab(self.scheduler_tab, self.tr("scheduler"))
        self.tabs.addTab(self.settings_tab, self.tr("settings"))
        
        # Add widgets to main layout
        main_layout.addWidget(header)
        main_layout.addWidget(self.optimize_btn)
        main_layout.addWidget(self.tabs)
        
        # Setup system monitoring
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_system_stats)
        self.monitor_timer.start(1000)  # Update every second
        
        # Initialize stats
        self.update_system_stats()

    def create_health_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # System Health Score
        health_group = QGroupBox("System Health Score")
        health_layout = QVBoxLayout(health_group)

        self.health_score_label = QLabel("Calculating...")
        self.health_score_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.health_score_label.setAlignment(Qt.AlignCenter)

        self.health_progress = QProgressBar()
        self.health_progress.setRange(0, 100)
        self.health_progress.setValue(0)
        self.health_progress.setTextVisible(False)
        self.health_progress.setFixedHeight(20)

        health_layout.addWidget(self.health_score_label)
        health_layout.addWidget(self.health_progress)

        # Recommendations
        recommendations_group = QGroupBox("Recommendations")
        recommendations_layout = QVBoxLayout(recommendations_group)

        self.recommendations_label = QLabel("Analyzing system...")
        self.recommendations_label.setWordWrap(True)
        recommendations_layout.addWidget(self.recommendations_label)

        # Quick Actions
        actions_group = QGroupBox("Quick Actions")
        actions_layout = QHBoxLayout(actions_group)

        quick_clean_btn = QPushButton("Quick Clean")
        quick_clean_btn.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")
        quick_clean_btn.clicked.connect(self.quick_clean)

        refresh_health_btn = QPushButton("Refresh Health")
        refresh_health_btn.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold;")
        refresh_health_btn.clicked.connect(self.calculate_health_score)

        actions_layout.addWidget(quick_clean_btn)
        actions_layout.addWidget(refresh_health_btn)
        actions_layout.addStretch()

        layout.addWidget(health_group)
        layout.addWidget(recommendations_group)
        layout.addWidget(actions_group)
        layout.addStretch()

        # Calculate initial health score
        QTimer.singleShot(1000, self.calculate_health_score)  # Delay to allow UI to load

        return tab

    def calculate_health_score(self):
        """Calculate system health score based on various metrics"""
        try:
            score = 100
            recommendations = []

            # CPU usage check
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 80:
                score -= 15
                recommendations.append("• High CPU usage detected. Consider closing unnecessary programs.")
            elif cpu_percent > 60:
                score -= 8
                recommendations.append("• Moderate CPU usage. Monitor running processes.")

            # Memory usage check
            ram = psutil.virtual_memory()
            if ram.percent > 85:
                score -= 20
                recommendations.append("• High memory usage. Consider closing memory-intensive applications.")
            elif ram.percent > 70:
                score -= 10
                recommendations.append("• Moderate memory usage. Monitor RAM consumption.")

            # Disk usage check
            try:
                disk = psutil.disk_usage('C:')
                if disk.percent > 90:
                    score -= 25
                    recommendations.append("• Disk space critically low. Clean up files immediately.")
                elif disk.percent > 80:
                    score -= 15
                    recommendations.append("• Disk space running low. Consider cleaning temporary files.")
            except:
                pass

            # Process count check
            process_count = len(psutil.pids())
            if process_count > 200:
                score -= 10
                recommendations.append("• High number of running processes. Consider disabling startup programs.")

            # Temperature check (if available)
            try:
                temps = psutil.sensors_temperatures()
                if temps:
                    for name, entries in temps.items():
                        if 'cpu' in name.lower() or 'core' in name.lower():
                            if entries and entries[0].current > 80:
                                score -= 15
                                recommendations.append("• High CPU temperature detected. Check cooling system.")
                            break
            except:
                pass

            # Ensure score doesn't go below 0
            score = max(0, score)

            # Update UI
            self.health_score_label.setText(f"{score}/100")
            self.health_progress.setValue(score)

            # Set color based on score
            if score >= 80:
                color = "#4CAF50"  # Green
                status = "Excellent"
            elif score >= 60:
                color = "#FF9800"  # Orange
                status = "Good"
            elif score >= 40:
                color = "#FF5722"  # Red-Orange
                status = "Fair"
            else:
                color = "#f44336"  # Red
                status = "Poor"

            self.health_score_label.setStyleSheet(f"color: {color};")
            self.health_progress.setStyleSheet(f"QProgressBar::chunk {{ background-color: {color}; }}")

            # Update recommendations
            if not recommendations:
                recommendations.append("• System is running optimally!")

            recommendations_text = f"System Status: {status}\n\n" + "\n".join(recommendations)
            self.recommendations_label.setText(recommendations_text)

        except Exception as e:
            self.health_score_label.setText("Error")
            self.recommendations_label.setText(f"Error calculating health score: {str(e)}")

    def quick_clean(self):
        """Perform quick system cleanup"""
        try:
            # Run basic cleanup operations
            self.optimizer.clean_temp_files()
            self.optimizer.clean_browser_cache()

            QMessageBox.information(self, 'Success', 'Quick cleanup completed!')

            # Refresh health score
            QTimer.singleShot(1000, self.calculate_health_score)

        except Exception as e:
            QMessageBox.critical(self, 'Error', f'Quick cleanup failed: {str(e)}')

    def create_stat_widget(self, title, value):
        container = QGroupBox(title)
        container.setStyleSheet("QGroupBox { border: 1px solid gray; border-radius: 5px; margin-top: 1ex; }"
                                "QGroupBox::title { subcontrol-origin: margin; padding: 0 3px; }")
        layout = QVBoxLayout(container)
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 16, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        
        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(0)
        progress.setTextVisible(False)
        
        layout.addWidget(value_label)
        layout.addWidget(progress)
        
        return container
    
    def update_system_stats(self):
        # CPU usage
        cpu_percent = psutil.cpu_percent()
        self.cpu_label.findChild(QLabel).setText(f"{cpu_percent}%")
        self.cpu_label.findChild(QProgressBar).setValue(int(cpu_percent))

        # RAM usage
        ram = psutil.virtual_memory()
        ram_percent = ram.percent
        ram_gb = ram.used / (1024**3)
        total_gb = ram.total / (1024**3)
        self.ram_label.findChild(QLabel).setText(f"{ram_percent}%\n{ram_gb:.1f}/{total_gb:.1f} GB")
        self.ram_label.findChild(QProgressBar).setValue(int(ram_percent))

        # Disk usage (Windows C: drive)
        try:
            disk = psutil.disk_usage('C:')
            disk_percent = disk.percent
            disk_gb = disk.used / (1024**3)
            total_disk_gb = disk.total / (1024**3)
            self.disk_label.findChild(QLabel).setText(f"{disk_percent}%\n{disk_gb:.0f}/{total_disk_gb:.0f} GB")
            self.disk_label.findChild(QProgressBar).setValue(int(disk_percent))
        except:
            self.disk_label.findChild(QLabel).setText("N/A")
            self.disk_label.findChild(QProgressBar).setValue(0)

        # Network usage
        try:
            current_network = psutil.net_io_counters()
            bytes_sent = current_network.bytes_sent - self.last_network_stats.bytes_sent
            bytes_recv = current_network.bytes_recv - self.last_network_stats.bytes_recv
            total_bytes = bytes_sent + bytes_recv

            # Convert to KB/s (update interval is 1 second)
            speed_kbs = total_bytes / 1024
            if speed_kbs > 1024:
                speed_text = f"{speed_kbs/1024:.1f} MB/s"
                progress_val = min(int(speed_kbs / 1024), 100)  # Cap at 100 MB/s for progress bar
            else:
                speed_text = f"{speed_kbs:.0f} KB/s"
                progress_val = min(int(speed_kbs / 10), 100)  # Cap at 1 MB/s for progress bar

            self.network_label.findChild(QLabel).setText(speed_text)
            self.network_label.findChild(QProgressBar).setValue(progress_val)
            self.last_network_stats = current_network
        except:
            self.network_label.findChild(QLabel).setText("N/A")
            self.network_label.findChild(QProgressBar).setValue(0)

        # Temperature (if available)
        try:
            temps = psutil.sensors_temperatures()
            if temps:
                # Try to get CPU temperature
                cpu_temp = None
                for name, entries in temps.items():
                    if 'cpu' in name.lower() or 'core' in name.lower():
                        if entries:
                            cpu_temp = entries[0].current
                            break

                if cpu_temp:
                    self.temp_label.findChild(QLabel).setText(f"{cpu_temp:.0f}°C")
                    # Temperature progress bar: 0-100°C scale
                    temp_progress = min(int(cpu_temp), 100)
                    self.temp_label.findChild(QProgressBar).setValue(temp_progress)
                else:
                    self.temp_label.findChild(QLabel).setText("N/A")
                    self.temp_label.findChild(QProgressBar).setValue(0)
            else:
                self.temp_label.findChild(QLabel).setText("N/A")
                self.temp_label.findChild(QProgressBar).setValue(0)
        except:
            self.temp_label.findChild(QLabel).setText("N/A")
            self.temp_label.findChild(QProgressBar).setValue(0)
    
    def tr(self, key):
        """Get translation for key"""
        return self.optimizer.tr(key)
    
    def update_title(self):
        """Update window title with translated app name"""
        self.setWindowTitle(self.tr("app_title"))
    
    def apply_theme(self):
        """Apply light/dark theme based on config"""
        theme = self.optimizer.config.get('theme', 'light')
        
        if theme == 'dark':
            self.setStyleSheet("""
                QMainWindow, QWidget {
                    background-color: #333333;
                    color: #ffffff;
                }
                QPushButton {
                    background-color: #555555;
                    color: white;
                    border: 1px solid #666666;
                    border-radius: 4px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #666666;
                }
                QTabWidget::pane {
                    border: 1px solid #444444;
                }
                QTabBar::tab {
                    background: #444444;
                    color: #ffffff;
                    padding: 8px;
                }
                QTabBar::tab:selected {
                    background: #555555;
                    border-bottom: 2px solid #4CAF50;
                }
                QGroupBox {
                    color: #ffffff;
                }
            """)
        else:
            self.setStyleSheet("")
    
    def on_config_changed(self):
        """Handle configuration changes"""
        self.apply_theme()
        self.update_title()
        self.retranslate_ui()
    
    def retranslate_ui(self):
        """Update all UI text with new translations"""
        # Update header labels
        self.cpu_label.setTitle(self.tr("cpu"))
        self.ram_label.setTitle(self.tr("ram"))
        self.disk_label.setTitle(self.tr("disk"))
        self.network_label.setTitle(self.tr("network"))
        self.temp_label.setTitle(self.tr("temperature"))
        
        # Update button text
        self.optimize_btn.setText(self.tr("optimize_now"))
        
        # Update tab names
        self.tabs.setTabText(0, self.tr("system_health"))
        self.tabs.setTabText(1, self.tr("cleaner"))
        self.tabs.setTabText(2, self.tr("startup"))
        self.tabs.setTabText(3, self.tr("performance"))
        self.tabs.setTabText(4, self.tr("processes"))
        self.tabs.setTabText(5, self.tr("gaming_mode"))
        self.tabs.setTabText(6, self.tr("scheduler"))
        self.tabs.setTabText(7, self.tr("settings"))
        
        # Update settings tab
        self.language_label.setText(self.tr("language"))
        self.theme_label.setText(self.tr("theme"))
        self.clean_temp_check.setText(self.tr("clean_temp_files"))
        self.clean_cache_check.setText(self.tr("clean_browser_cache"))
        self.disable_startup_check.setText(self.tr("disable_startup"))
        self.disable_telemetry_check.setText(self.tr("disable_telemetry"))
        self.optimize_disk_check.setText(self.tr("optimize_disk"))
        self.detect_bloatware_check.setText(self.tr("detect_bloatware"))
    
    def create_cleaner_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Basic cleaning options
        basic_group = QGroupBox("Basic Cleaning")
        basic_layout = QVBoxLayout(basic_group)

        self.clean_temp_check = QCheckBox(self.tr("clean_temp_files"))
        self.clean_temp_check.setChecked(True)

        self.clean_cache_check = QCheckBox(self.tr("clean_browser_cache"))
        self.clean_cache_check.setChecked(True)

        self.clean_logs_check = QCheckBox("Clean System Logs")
        self.clean_logs_check.setChecked(True)

        basic_layout.addWidget(self.clean_temp_check)
        basic_layout.addWidget(self.clean_cache_check)
        basic_layout.addWidget(self.clean_logs_check)

        # Advanced cleaning options
        advanced_group = QGroupBox("Advanced Cleaning")
        advanced_layout = QVBoxLayout(advanced_group)

        self.clean_registry_check = QCheckBox("Clean Invalid Registry Entries")
        self.clean_registry_check.setChecked(False)

        advanced_layout.addWidget(self.clean_registry_check)

        # Duplicate files section
        duplicates_group = QGroupBox("Duplicate Files")
        duplicates_layout = QVBoxLayout(duplicates_group)

        find_duplicates_btn = QPushButton("Find Duplicate Files")
        find_duplicates_btn.clicked.connect(self.find_duplicates)

        self.duplicates_label = QLabel("Click 'Find Duplicate Files' to scan for duplicates")
        self.duplicates_label.setWordWrap(True)

        duplicates_layout.addWidget(find_duplicates_btn)
        duplicates_layout.addWidget(self.duplicates_label)

        # Large files section
        large_files_group = QGroupBox("Large Files")
        large_files_layout = QVBoxLayout(large_files_group)

        find_large_btn = QPushButton("Find Large Files (>100MB)")
        find_large_btn.clicked.connect(self.find_large_files)

        self.large_files_label = QLabel("Click 'Find Large Files' to scan for space-consuming files")
        self.large_files_label.setWordWrap(True)

        large_files_layout.addWidget(find_large_btn)
        large_files_layout.addWidget(self.large_files_label)

        layout.addWidget(basic_group)
        layout.addWidget(advanced_group)
        layout.addWidget(duplicates_group)
        layout.addWidget(large_files_group)
        layout.addStretch()

        return tab

    def find_duplicates(self):
        """Find and display duplicate files"""
        try:
            self.duplicates_label.setText("Scanning for duplicate files...")
            duplicates = self.optimizer.find_duplicate_files()

            if duplicates:
                total_duplicates = sum(len(group) - 1 for group in duplicates)  # -1 because we keep one copy
                total_size = 0

                # Calculate total size of duplicates
                for group in duplicates:
                    for i, file_path in enumerate(group[1:], 1):  # Skip first file (keep it)
                        try:
                            total_size += os.path.getsize(file_path)
                        except:
                            pass

                size_mb = total_size / (1024 * 1024)

                message = f"Found {total_duplicates} duplicate files in {len(duplicates)} groups.\n"
                message += f"Potential space savings: {size_mb:.2f} MB\n\n"
                message += "First few duplicate groups:\n"

                for i, group in enumerate(duplicates[:3]):  # Show first 3 groups
                    message += f"\nGroup {i+1}:\n"
                    for file_path in group:
                        message += f"  • {file_path}\n"

                if len(duplicates) > 3:
                    message += f"\n... and {len(duplicates) - 3} more groups"

                self.duplicates_label.setText(message)

                # Ask if user wants to delete duplicates
                reply = QMessageBox.question(
                    self, 'Delete Duplicates?',
                    f'Found {total_duplicates} duplicate files. Delete them to free {size_mb:.2f} MB?',
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    deleted_count = 0
                    for group in duplicates:
                        for file_path in group[1:]:  # Keep first file, delete others
                            try:
                                os.remove(file_path)
                                deleted_count += 1
                            except:
                                pass

                    QMessageBox.information(self, 'Success', f'Deleted {deleted_count} duplicate files!')
                    self.duplicates_label.setText(f"Deleted {deleted_count} duplicate files, freed {size_mb:.2f} MB")
            else:
                self.duplicates_label.setText("No duplicate files found!")

        except Exception as e:
            self.duplicates_label.setText(f"Error scanning for duplicates: {str(e)}")

    def find_large_files(self):
        """Find and display large files"""
        try:
            self.large_files_label.setText("Scanning for large files...")
            large_files = self.optimizer.find_large_files()

            if large_files:
                total_size = sum(f['size_mb'] for f in large_files)

                message = f"Found {len(large_files)} large files (>100MB).\n"
                message += f"Total size: {total_size:.2f} MB\n\n"
                message += "Largest files:\n"

                for i, file_info in enumerate(large_files[:5]):  # Show top 5
                    message += f"  • {os.path.basename(file_info['path'])} - {file_info['size_mb']:.1f} MB\n"
                    message += f"    {file_info['path']}\n"

                if len(large_files) > 5:
                    message += f"\n... and {len(large_files) - 5} more files"

                self.large_files_label.setText(message)
            else:
                self.large_files_label.setText("No large files found!")

        except Exception as e:
            self.large_files_label.setText(f"Error scanning for large files: {str(e)}")
    
    def create_startup_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.disable_startup_check = QCheckBox(self.tr("disable_startup"))
        self.disable_startup_check.setChecked(True)
        
        layout.addWidget(self.disable_startup_check)
        layout.addStretch()
        
        return tab
    
    def create_performance_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.disable_telemetry_check = QCheckBox(self.tr("disable_telemetry"))
        self.disable_telemetry_check.setChecked(True)
        
        self.optimize_disk_check = QCheckBox(self.tr("optimize_disk"))
        self.optimize_disk_check.setChecked(True)
        
        self.detect_bloatware_check = QCheckBox(self.tr("detect_bloatware"))
        self.detect_bloatware_check.setChecked(True)
        
        layout.addWidget(self.disable_telemetry_check)
        layout.addWidget(self.optimize_disk_check)
        layout.addWidget(self.detect_bloatware_check)
        layout.addStretch()
        
        return tab

    def create_processes_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Control buttons
        button_layout = QHBoxLayout()
        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self.refresh_processes)
        end_process_btn = QPushButton("End Process")
        end_process_btn.clicked.connect(self.end_selected_process)

        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(end_process_btn)
        button_layout.addStretch()

        # Process table
        self.process_table = QTableWidget()
        self.process_table.setColumnCount(5)
        self.process_table.setHorizontalHeaderLabels(["PID", "Name", "CPU %", "Memory (MB)", "Status"])
        self.process_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.process_table.setAlternatingRowColors(True)

        # Set column widths
        header = self.process_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # PID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Name
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # CPU
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Memory
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Status

        layout.addLayout(button_layout)
        layout.addWidget(self.process_table)

        # Initialize process list
        self.refresh_processes()

        return tab

    def refresh_processes(self):
        """Refresh the process list"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'status']):
                try:
                    pinfo = proc.info
                    memory_mb = pinfo['memory_info'].rss / 1024 / 1024 if pinfo['memory_info'] else 0
                    processes.append({
                        'pid': pinfo['pid'],
                        'name': pinfo['name'] or 'N/A',
                        'cpu_percent': pinfo['cpu_percent'] or 0,
                        'memory_mb': memory_mb,
                        'status': pinfo['status'] or 'unknown'
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass

            # Sort by CPU usage (descending)
            processes.sort(key=lambda x: x['cpu_percent'], reverse=True)

            # Update table
            self.process_table.setRowCount(len(processes))
            for i, proc in enumerate(processes):
                self.process_table.setItem(i, 0, QTableWidgetItem(str(proc['pid'])))
                self.process_table.setItem(i, 1, QTableWidgetItem(proc['name']))
                self.process_table.setItem(i, 2, QTableWidgetItem(f"{proc['cpu_percent']:.1f}"))
                self.process_table.setItem(i, 3, QTableWidgetItem(f"{proc['memory_mb']:.1f}"))
                self.process_table.setItem(i, 4, QTableWidgetItem(proc['status']))

        except Exception as e:
            print(f"Error refreshing processes: {e}")

    def end_selected_process(self):
        """End the selected process"""
        current_row = self.process_table.currentRow()
        if current_row >= 0:
            pid_item = self.process_table.item(current_row, 0)
            name_item = self.process_table.item(current_row, 1)

            if pid_item and name_item:
                pid = int(pid_item.text())
                name = name_item.text()

                reply = QMessageBox.question(
                    self, 'Confirm',
                    f'Are you sure you want to end process "{name}" (PID: {pid})?',
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    try:
                        proc = psutil.Process(pid)
                        proc.terminate()
                        self.refresh_processes()
                        QMessageBox.information(self, 'Success', f'Process "{name}" terminated successfully.')
                    except psutil.NoSuchProcess:
                        QMessageBox.warning(self, 'Error', 'Process no longer exists.')
                        self.refresh_processes()
                    except psutil.AccessDenied:
                        QMessageBox.warning(self, 'Error', 'Access denied. Cannot terminate this process.')
                    except Exception as e:
                        QMessageBox.critical(self, 'Error', f'Failed to terminate process: {str(e)}')

    def create_gaming_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Gaming mode status
        status_group = QGroupBox("Gaming Mode Status")
        status_layout = QVBoxLayout(status_group)

        self.gaming_status_label = QLabel("Gaming Mode: Disabled")
        self.gaming_status_label.setFont(QFont("Arial", 12, QFont.Bold))
        status_layout.addWidget(self.gaming_status_label)

        # Gaming mode controls
        controls_layout = QHBoxLayout()
        self.enable_gaming_btn = QPushButton("Enable Gaming Mode")
        self.enable_gaming_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.enable_gaming_btn.clicked.connect(self.toggle_gaming_mode)

        self.disable_gaming_btn = QPushButton("Disable Gaming Mode")
        self.disable_gaming_btn.setStyleSheet("background-color: #f44336; color: white; font-weight: bold;")
        self.disable_gaming_btn.clicked.connect(self.toggle_gaming_mode)
        self.disable_gaming_btn.setEnabled(False)

        controls_layout.addWidget(self.enable_gaming_btn)
        controls_layout.addWidget(self.disable_gaming_btn)

        # Gaming mode description
        description_group = QGroupBox("What Gaming Mode Does")
        description_layout = QVBoxLayout(description_group)

        description_text = QLabel("""
Gaming Mode optimizes your system for maximum performance by:

• Stopping non-essential Windows services
• Setting high-performance power plan
• Reducing background processes
• Optimizing system resources for games

Note: Some system features may be temporarily disabled.
You can restore normal operation by disabling Gaming Mode.
        """)
        description_text.setWordWrap(True)
        description_layout.addWidget(description_text)

        layout.addWidget(status_group)
        layout.addLayout(controls_layout)
        layout.addWidget(description_group)
        layout.addStretch()

        # Update gaming mode status
        self.update_gaming_status()

        return tab

    def toggle_gaming_mode(self):
        """Toggle gaming mode on/off"""
        sender = self.sender()

        if sender == self.enable_gaming_btn:
            success = self.optimizer.enable_gaming_mode()
            if success:
                QMessageBox.information(self, 'Success', 'Gaming Mode enabled successfully!')
            else:
                QMessageBox.warning(self, 'Error', 'Failed to enable Gaming Mode.')
        else:
            success = self.optimizer.disable_gaming_mode()
            if success:
                QMessageBox.information(self, 'Success', 'Gaming Mode disabled successfully!')
            else:
                QMessageBox.warning(self, 'Error', 'Failed to disable Gaming Mode.')

        self.update_gaming_status()

    def update_gaming_status(self):
        """Update gaming mode status display"""
        is_active = self.optimizer.get_gaming_mode_status()

        if is_active:
            self.gaming_status_label.setText("Gaming Mode: Enabled")
            self.gaming_status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            self.enable_gaming_btn.setEnabled(False)
            self.disable_gaming_btn.setEnabled(True)
        else:
            self.gaming_status_label.setText("Gaming Mode: Disabled")
            self.gaming_status_label.setStyleSheet("color: #f44336; font-weight: bold;")
            self.enable_gaming_btn.setEnabled(True)
            self.disable_gaming_btn.setEnabled(False)

    def create_scheduler_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Scheduler status
        status_group = QGroupBox("Automatic Optimization Schedule")
        status_layout = QVBoxLayout(status_group)

        self.scheduler_enabled_check = QCheckBox("Enable Automatic Optimization")
        self.scheduler_enabled_check.setChecked(self.optimizer.config.get('auto_optimize', False))
        self.scheduler_enabled_check.toggled.connect(self.toggle_scheduler)

        # Schedule frequency
        frequency_layout = QHBoxLayout()
        frequency_layout.addWidget(QLabel("Run optimization every:"))

        self.frequency_combo = QComboBox()
        self.frequency_combo.addItem("Daily", "daily")
        self.frequency_combo.addItem("Weekly", "weekly")
        self.frequency_combo.addItem("Monthly", "monthly")

        current_freq = self.optimizer.config.get('auto_optimize_frequency', 'weekly')
        for i in range(self.frequency_combo.count()):
            if self.frequency_combo.itemData(i) == current_freq:
                self.frequency_combo.setCurrentIndex(i)
                break

        self.frequency_combo.currentIndexChanged.connect(self.update_frequency)

        frequency_layout.addWidget(self.frequency_combo)
        frequency_layout.addStretch()

        # Last run info
        self.last_run_label = QLabel("Last automatic optimization: Never")
        last_run = self.optimizer.config.get('last_auto_optimize', None)
        if last_run:
            self.last_run_label.setText(f"Last automatic optimization: {last_run}")

        # Next run info
        self.next_run_label = QLabel("Next automatic optimization: Not scheduled")
        self.update_next_run_display()

        status_layout.addWidget(self.scheduler_enabled_check)
        status_layout.addLayout(frequency_layout)
        status_layout.addWidget(self.last_run_label)
        status_layout.addWidget(self.next_run_label)

        # Manual controls
        controls_group = QGroupBox("Manual Controls")
        controls_layout = QHBoxLayout(controls_group)

        run_now_btn = QPushButton("Run Optimization Now")
        run_now_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        run_now_btn.clicked.connect(self.run_scheduled_optimization)

        test_schedule_btn = QPushButton("Test Schedule")
        test_schedule_btn.clicked.connect(self.test_schedule)

        controls_layout.addWidget(run_now_btn)
        controls_layout.addWidget(test_schedule_btn)
        controls_layout.addStretch()

        # Options for scheduled optimization
        options_group = QGroupBox("Scheduled Optimization Options")
        options_layout = QVBoxLayout(options_group)

        self.schedule_clean_temp = QCheckBox("Clean temporary files")
        self.schedule_clean_temp.setChecked(True)

        self.schedule_clean_cache = QCheckBox("Clean browser cache")
        self.schedule_clean_cache.setChecked(True)

        self.schedule_clean_logs = QCheckBox("Clean system logs")
        self.schedule_clean_logs.setChecked(True)

        self.schedule_optimize_disk = QCheckBox("Optimize disk")
        self.schedule_optimize_disk.setChecked(True)

        options_layout.addWidget(self.schedule_clean_temp)
        options_layout.addWidget(self.schedule_clean_cache)
        options_layout.addWidget(self.schedule_clean_logs)
        options_layout.addWidget(self.schedule_optimize_disk)

        layout.addWidget(status_group)
        layout.addWidget(controls_group)
        layout.addWidget(options_group)
        layout.addStretch()

        return tab

    def toggle_scheduler(self, enabled):
        """Toggle automatic optimization scheduler"""
        self.optimizer.config['auto_optimize'] = enabled
        self.optimizer.save_config()
        self.update_next_run_display()

        if enabled:
            QMessageBox.information(self, 'Scheduler Enabled',
                                  'Automatic optimization has been enabled. The system will be optimized according to your schedule.')
        else:
            QMessageBox.information(self, 'Scheduler Disabled',
                                  'Automatic optimization has been disabled.')

    def update_frequency(self, index):
        """Update optimization frequency"""
        frequency = self.frequency_combo.currentData()
        self.optimizer.config['auto_optimize_frequency'] = frequency
        self.optimizer.save_config()
        self.update_next_run_display()

    def update_next_run_display(self):
        """Update the next run display"""
        if not self.optimizer.config.get('auto_optimize', False):
            self.next_run_label.setText("Next automatic optimization: Not scheduled")
            return

        frequency = self.optimizer.config.get('auto_optimize_frequency', 'weekly')
        last_run = self.optimizer.config.get('last_auto_optimize', None)

        if last_run:
            from datetime import datetime, timedelta
            try:
                last_date = datetime.fromisoformat(last_run)
                if frequency == 'daily':
                    next_date = last_date + timedelta(days=1)
                elif frequency == 'weekly':
                    next_date = last_date + timedelta(weeks=1)
                else:  # monthly
                    next_date = last_date + timedelta(days=30)

                self.next_run_label.setText(f"Next automatic optimization: {next_date.strftime('%Y-%m-%d %H:%M')}")
            except:
                self.next_run_label.setText("Next automatic optimization: Error calculating")
        else:
            self.next_run_label.setText("Next automatic optimization: Will run on next startup")

    def run_scheduled_optimization(self):
        """Run optimization with scheduled options"""
        try:
            results = []

            if self.schedule_clean_temp.isChecked():
                self.optimizer.clean_temp_files()
                results.append("Cleaned temporary files")

            if self.schedule_clean_cache.isChecked():
                self.optimizer.clean_browser_cache()
                results.append("Cleaned browser cache")

            if self.schedule_clean_logs.isChecked():
                freed_mb = self.optimizer.clean_system_logs()
                results.append(f"Cleaned system logs ({freed_mb:.1f} MB freed)")

            if self.schedule_optimize_disk.isChecked():
                self.optimizer.optimize_disk()
                results.append("Optimized disk performance")

            # Update last run time
            from datetime import datetime
            self.optimizer.config['last_auto_optimize'] = datetime.now().isoformat()
            self.optimizer.save_config()

            # Update displays
            self.last_run_label.setText(f"Last automatic optimization: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
            self.update_next_run_display()

            # Show results
            result_text = "Scheduled optimization completed!\n\n" + "\n".join(f"• {result}" for result in results)
            QMessageBox.information(self, 'Optimization Complete', result_text)

            # Refresh health score if available
            if hasattr(self, 'calculate_health_score'):
                QTimer.singleShot(2000, self.calculate_health_score)

        except Exception as e:
            QMessageBox.critical(self, 'Error', f'Scheduled optimization failed: {str(e)}')

    def test_schedule(self):
        """Test the scheduling system"""
        frequency = self.optimizer.config.get('auto_optimize_frequency', 'weekly')
        enabled = self.optimizer.config.get('auto_optimize', False)

        if enabled:
            QMessageBox.information(self, 'Schedule Test',
                                  f'Scheduler is enabled and set to run {frequency}.\n'
                                  f'The optimization will run automatically according to your schedule.')
        else:
            QMessageBox.warning(self, 'Schedule Test',
                               'Scheduler is currently disabled. Enable it to use automatic optimization.')

    def create_settings_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Language selection
        language_group = QGroupBox(self.tr("language"))
        language_layout = QVBoxLayout(language_group)
        self.language_combo = QComboBox()
        self.language_combo.addItem("English", "en")
        self.language_combo.addItem("العربية", "ar")
        self.language_combo.setCurrentText("English" if self.optimizer.config.get('language', 'en') == 'en' else "العربية")
        self.language_combo.currentIndexChanged.connect(self.change_language)
        language_layout.addWidget(self.language_combo)
        
        # Theme selection
        theme_group = QGroupBox(self.tr("theme"))
        theme_layout = QVBoxLayout(theme_group)
        self.theme_combo = QComboBox()
        self.theme_combo.addItem(self.tr("light"), "light")
        self.theme_combo.addItem(self.tr("dark"), "dark")
        self.theme_combo.setCurrentText(self.tr("light") if self.optimizer.config.get('theme', 'light') == 'light' else self.tr("dark"))
        self.theme_combo.currentIndexChanged.connect(self.change_theme)
        theme_layout.addWidget(self.theme_combo)
        
        layout.addWidget(language_group)
        layout.addWidget(theme_group)
        layout.addStretch()
        
        return tab
    
    def change_language(self, index):
        lang = self.language_combo.currentData()
        self.optimizer.config['language'] = lang
        self.optimizer.save_config()
        self.configChanged.emit()
    
    def change_theme(self, index):
        theme = self.theme_combo.currentData()
        self.optimizer.config['theme'] = theme
        self.optimizer.save_config()
        self.configChanged.emit()
    
    def run_optimization(self):
        # This will trigger the optimization process with selected options
        try:
            results = []

            # Basic cleaning
            if hasattr(self, 'clean_temp_check') and self.clean_temp_check.isChecked():
                self.optimizer.clean_temp_files()
                results.append("Cleaned temporary files")

            if hasattr(self, 'clean_cache_check') and self.clean_cache_check.isChecked():
                self.optimizer.clean_browser_cache()
                results.append("Cleaned browser cache")

            if hasattr(self, 'clean_logs_check') and self.clean_logs_check.isChecked():
                freed_mb = self.optimizer.clean_system_logs()
                results.append(f"Cleaned system logs ({freed_mb:.1f} MB freed)")

            # Advanced cleaning
            if hasattr(self, 'clean_registry_check') and self.clean_registry_check.isChecked():
                cleaned_entries = self.optimizer.clean_registry()
                results.append(f"Cleaned registry ({cleaned_entries} invalid entries removed)")

            # Performance optimizations
            if hasattr(self, 'disable_startup_check') and self.disable_startup_check.isChecked():
                self.optimizer.disable_startup_programs()
                results.append("Disabled unnecessary startup programs")

            if hasattr(self, 'disable_telemetry_check') and self.disable_telemetry_check.isChecked():
                self.optimizer.disable_telemetry()
                results.append("Disabled telemetry and tracking")

            if hasattr(self, 'optimize_disk_check') and self.optimize_disk_check.isChecked():
                self.optimizer.optimize_disk()
                results.append("Optimized disk performance")

            if hasattr(self, 'detect_bloatware_check') and self.detect_bloatware_check.isChecked():
                bloatware = self.optimizer.detect_bloatware()
                if bloatware:
                    self.optimizer.remove_bloatware(bloatware)
                    results.append(f"Removed {len(bloatware)} bloatware applications")

            # Show results
            if results:
                result_text = "Optimization completed successfully!\n\n" + "\n".join(f"• {result}" for result in results)
                QMessageBox.information(self, 'Optimization Complete', result_text)

                # Refresh health score if available
                if hasattr(self, 'calculate_health_score'):
                    QTimer.singleShot(2000, self.calculate_health_score)
            else:
                QMessageBox.information(self, 'No Actions', 'No optimization options were selected.')

        except Exception as e:
            QMessageBox.critical(self, 'Error', f'Optimization failed: {str(e)}')