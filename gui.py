from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QTabWidget, QProgressBar, QGroupBox, QStackedWidget,
    QComboBox, QCheckBox
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QIcon, QFont, QPalette, QColor
import psutil

class MainWindow(QMainWindow):
    configChanged = Signal()
    
    def __init__(self, optimizer):
        super().__init__()
        self.optimizer = optimizer
        self.setMinimumSize(800, 600)
        self.update_title()
        
        # Central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Apply theme
        self.apply_theme()
        
        # Connect config change signal
        self.configChanged.connect(self.on_config_changed)
        
        # Header with system stats
        header = QWidget()
        header_layout = QHBoxLayout(header)
        
        self.cpu_label = self.create_stat_widget(self.tr("cpu"), "0%")
        self.ram_label = self.create_stat_widget(self.tr("ram"), "0%")
        self.disk_label = self.create_stat_widget(self.tr("disk"), "0%")
        
        header_layout.addWidget(self.cpu_label)
        header_layout.addWidget(self.ram_label)
        header_layout.addWidget(self.disk_label)
        header_layout.addStretch()
        
        # Optimization button
        self.optimize_btn = QPushButton(self.tr("optimize_now"))
        self.optimize_btn.setFixedHeight(40)
        self.optimize_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.optimize_btn.clicked.connect(self.run_optimization)
        
        # Tab-based interface
        self.tabs = QTabWidget()
        self.cleaner_tab = self.create_cleaner_tab()
        self.startup_tab = self.create_startup_tab()
        self.performance_tab = self.create_performance_tab()
        self.settings_tab = self.create_settings_tab()
        
        self.tabs.addTab(self.cleaner_tab, self.tr("cleaner"))
        self.tabs.addTab(self.startup_tab, self.tr("startup"))
        self.tabs.addTab(self.performance_tab, self.tr("performance"))
        self.tabs.addTab(self.settings_tab, self.tr("settings"))
        
        # Add widgets to main layout
        main_layout.addWidget(header)
        main_layout.addWidget(self.optimize_btn)
        main_layout.addWidget(self.tabs)
        
        # Setup system monitoring
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_system_stats)
        self.monitor_timer.start(1000)  # Update every second
        
        # Initialize stats
        self.update_system_stats()
    
    def create_stat_widget(self, title, value):
        container = QGroupBox(title)
        container.setStyleSheet("QGroupBox { border: 1px solid gray; border-radius: 5px; margin-top: 1ex; }"
                                "QGroupBox::title { subcontrol-origin: margin; padding: 0 3px; }")
        layout = QVBoxLayout(container)
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 16, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        
        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(0)
        progress.setTextVisible(False)
        
        layout.addWidget(value_label)
        layout.addWidget(progress)
        
        return container
    
    def update_system_stats(self):
        # CPU usage
        cpu_percent = psutil.cpu_percent()
        self.cpu_label.findChild(QLabel).setText(f"{cpu_percent}%")
        self.cpu_label.findChild(QProgressBar).setValue(int(cpu_percent))
        
        # RAM usage
        ram = psutil.virtual_memory()
        ram_percent = ram.percent
        self.ram_label.findChild(QLabel).setText(f"{ram_percent}%")
        self.ram_label.findChild(QProgressBar).setValue(int(ram_percent))
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        self.disk_label.findChild(QLabel).setText(f"{disk_percent}%")
        self.disk_label.findChild(QProgressBar).setValue(int(disk_percent))
    
    def tr(self, key):
        """Get translation for key"""
        return self.optimizer.tr(key)
    
    def update_title(self):
        """Update window title with translated app name"""
        self.setWindowTitle(self.tr("app_title"))
    
    def apply_theme(self):
        """Apply light/dark theme based on config"""
        theme = self.optimizer.config.get('theme', 'light')
        
        if theme == 'dark':
            self.setStyleSheet("""
                QMainWindow, QWidget {
                    background-color: #333333;
                    color: #ffffff;
                }
                QPushButton {
                    background-color: #555555;
                    color: white;
                    border: 1px solid #666666;
                    border-radius: 4px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #666666;
                }
                QTabWidget::pane {
                    border: 1px solid #444444;
                }
                QTabBar::tab {
                    background: #444444;
                    color: #ffffff;
                    padding: 8px;
                }
                QTabBar::tab:selected {
                    background: #555555;
                    border-bottom: 2px solid #4CAF50;
                }
                QGroupBox {
                    color: #ffffff;
                }
            """)
        else:
            self.setStyleSheet("")
    
    def on_config_changed(self):
        """Handle configuration changes"""
        self.apply_theme()
        self.update_title()
        self.retranslate_ui()
    
    def retranslate_ui(self):
        """Update all UI text with new translations"""
        # Update header labels
        self.cpu_label.setTitle(self.tr("cpu"))
        self.ram_label.setTitle(self.tr("ram"))
        self.disk_label.setTitle(self.tr("disk"))
        
        # Update button text
        self.optimize_btn.setText(self.tr("optimize_now"))
        
        # Update tab names
        self.tabs.setTabText(0, self.tr("cleaner"))
        self.tabs.setTabText(1, self.tr("startup"))
        self.tabs.setTabText(2, self.tr("performance"))
        self.tabs.setTabText(3, self.tr("settings"))
        
        # Update settings tab
        self.language_label.setText(self.tr("language"))
        self.theme_label.setText(self.tr("theme"))
        self.clean_temp_check.setText(self.tr("clean_temp_files"))
        self.clean_cache_check.setText(self.tr("clean_browser_cache"))
        self.disable_startup_check.setText(self.tr("disable_startup"))
        self.disable_telemetry_check.setText(self.tr("disable_telemetry"))
        self.optimize_disk_check.setText(self.tr("optimize_disk"))
        self.detect_bloatware_check.setText(self.tr("detect_bloatware"))
    
    def create_cleaner_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.clean_temp_check = QCheckBox(self.tr("clean_temp_files"))
        self.clean_temp_check.setChecked(True)
        
        self.clean_cache_check = QCheckBox(self.tr("clean_browser_cache"))
        self.clean_cache_check.setChecked(True)
        
        layout.addWidget(self.clean_temp_check)
        layout.addWidget(self.clean_cache_check)
        layout.addStretch()
        
        return tab
    
    def create_startup_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.disable_startup_check = QCheckBox(self.tr("disable_startup"))
        self.disable_startup_check.setChecked(True)
        
        layout.addWidget(self.disable_startup_check)
        layout.addStretch()
        
        return tab
    
    def create_performance_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.disable_telemetry_check = QCheckBox(self.tr("disable_telemetry"))
        self.disable_telemetry_check.setChecked(True)
        
        self.optimize_disk_check = QCheckBox(self.tr("optimize_disk"))
        self.optimize_disk_check.setChecked(True)
        
        self.detect_bloatware_check = QCheckBox(self.tr("detect_bloatware"))
        self.detect_bloatware_check.setChecked(True)
        
        layout.addWidget(self.disable_telemetry_check)
        layout.addWidget(self.optimize_disk_check)
        layout.addWidget(self.detect_bloatware_check)
        layout.addStretch()
        
        return tab
    
    def create_settings_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Language selection
        language_group = QGroupBox(self.tr("language"))
        language_layout = QVBoxLayout(language_group)
        self.language_combo = QComboBox()
        self.language_combo.addItem("English", "en")
        self.language_combo.addItem("العربية", "ar")
        self.language_combo.setCurrentText("English" if self.optimizer.config.get('language', 'en') == 'en' else "العربية")
        self.language_combo.currentIndexChanged.connect(self.change_language)
        language_layout.addWidget(self.language_combo)
        
        # Theme selection
        theme_group = QGroupBox(self.tr("theme"))
        theme_layout = QVBoxLayout(theme_group)
        self.theme_combo = QComboBox()
        self.theme_combo.addItem(self.tr("light"), "light")
        self.theme_combo.addItem(self.tr("dark"), "dark")
        self.theme_combo.setCurrentText(self.tr("light") if self.optimizer.config.get('theme', 'light') == 'light' else self.tr("dark"))
        self.theme_combo.currentIndexChanged.connect(self.change_theme)
        theme_layout.addWidget(self.theme_combo)
        
        layout.addWidget(language_group)
        layout.addWidget(theme_group)
        layout.addStretch()
        
        return tab
    
    def change_language(self, index):
        lang = self.language_combo.currentData()
        self.optimizer.config['language'] = lang
        self.optimizer.save_config()
        self.configChanged.emit()
    
    def change_theme(self, index):
        theme = self.theme_combo.currentData()
        self.optimizer.config['theme'] = theme
        self.optimizer.save_config()
        self.configChanged.emit()
    
    def run_optimization(self):
        # This will trigger the optimization process
        self.optimizer.run_full_optimization()