from PySide6.QtWidgets import (
    QMain<PERSON><PERSON>ow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QTabWidget, QProgressBar, QGroupBox, QStackedWidget,
    QComboBox, QCheckBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QAbstractItemView
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QIcon, QFont, QPalette, QColor
import psutil
import os

class MainWindow(QMainWindow):
    configChanged = Signal()
    
    def __init__(self, optimizer):
        super().__init__()
        self.optimizer = optimizer
        self.setMinimumSize(800, 600)
        self.update_title()
        
        # Central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Apply theme
        self.apply_theme()
        
        # Connect config change signal
        self.configChanged.connect(self.on_config_changed)
        
        # Enhanced Header with system stats
        header = self.create_system_monitoring_header()

        # Initialize network stats for calculation
        self.last_network_stats = psutil.net_io_counters()
        
        # Enhanced Optimization button
        self.optimize_btn = self.create_primary_button(self.tr("optimize_now"), "#27ae60", "🚀")
        self.optimize_btn.setFixedHeight(50)
        self.optimize_btn.clicked.connect(self.run_optimization)
        
        # Enhanced Tab-based interface with modern styling
        self.tabs = self.create_modern_tab_widget()
        self.setup_tabs()
        
        # Add widgets to main layout
        main_layout.addWidget(header)
        main_layout.addWidget(self.optimize_btn)
        main_layout.addWidget(self.tabs)
        
        # Setup system monitoring
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_system_stats)
        self.monitor_timer.start(1000)  # Update every second
        
        # Initialize stats
        self.update_system_stats()

    def create_system_monitoring_header(self):
        """Create an enhanced system monitoring header with modern design"""
        header = QWidget()
        header.setFixedHeight(120)
        header.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 12px;
                margin: 8px;
            }
        """)

        main_layout = QVBoxLayout(header)
        main_layout.setContentsMargins(20, 15, 20, 15)
        main_layout.setSpacing(10)

        # Title section
        title_layout = QHBoxLayout()
        title_label = QLabel("System Performance Monitor")
        title_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; background: transparent;")

        # Real-time indicator
        self.realtime_indicator = QLabel("● Live")
        self.realtime_indicator.setFont(QFont("Segoe UI", 10))
        self.realtime_indicator.setStyleSheet("color: #27ae60; background: transparent;")

        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.realtime_indicator)

        # Stats grid
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(15)

        self.cpu_card = self.create_modern_stat_card("CPU", "0%", "#e74c3c", "processor")
        self.ram_card = self.create_modern_stat_card("RAM", "0%", "#3498db", "memory")
        self.disk_card = self.create_modern_stat_card("Storage", "0%", "#9b59b6", "storage")
        self.network_card = self.create_modern_stat_card("Network", "0 KB/s", "#f39c12", "network")
        self.temp_card = self.create_modern_stat_card("Temp", "N/A", "#e67e22", "temperature")

        stats_layout.addWidget(self.cpu_card)
        stats_layout.addWidget(self.ram_card)
        stats_layout.addWidget(self.disk_card)
        stats_layout.addWidget(self.network_card)
        stats_layout.addWidget(self.temp_card)

        main_layout.addLayout(title_layout)
        main_layout.addLayout(stats_layout)

        return header

    def create_modern_stat_card(self, title, value, color, icon_type):
        """Create a modern stat card with enhanced visual design"""
        card = QWidget()
        card.setFixedSize(140, 70)
        card.setStyleSheet(f"""
            QWidget {{
                background: white;
                border: 2px solid {color};
                border-radius: 8px;
                margin: 2px;
            }}
            QWidget:hover {{
                background: #f8f9fa;
                border: 2px solid {self.darken_color(color)};
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(4)

        # Title with icon
        title_layout = QHBoxLayout()
        title_layout.setSpacing(6)

        # Icon (using Unicode symbols)
        icon_map = {
            "processor": "🖥️",
            "memory": "💾",
            "storage": "💿",
            "network": "🌐",
            "temperature": "🌡️"
        }

        icon_label = QLabel(icon_map.get(icon_type, "📊"))
        icon_label.setFont(QFont("Segoe UI", 12))
        icon_label.setStyleSheet("background: transparent;")

        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 9, QFont.Bold))
        title_label.setStyleSheet(f"color: {color}; background: transparent;")

        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # Value
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        value_label.setStyleSheet("color: #2c3e50; background: transparent;")
        value_label.setAlignment(Qt.AlignCenter)

        # Progress bar
        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(0)
        progress.setFixedHeight(6)
        progress.setTextVisible(False)
        progress.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                border-radius: 3px;
                background-color: #ecf0f1;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {color}, stop:1 {self.lighten_color(color)});
                border-radius: 3px;
            }}
        """)

        layout.addLayout(title_layout)
        layout.addWidget(value_label)
        layout.addWidget(progress)

        # Store references for updates
        card.value_label = value_label
        card.progress_bar = progress

        return card

    def darken_color(self, color):
        """Darken a hex color by 20%"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(max(0, int(c * 0.8)) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    def lighten_color(self, color):
        """Lighten a hex color by 20%"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        lightened = tuple(min(255, int(c * 1.2)) for c in rgb)
        return f"#{lightened[0]:02x}{lightened[1]:02x}{lightened[2]:02x}"

    def create_modern_tab_widget(self):
        """Create a modern styled tab widget"""
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: #ffffff;
                margin-top: 10px;
            }

            QTabWidget::tab-bar {
                alignment: center;
            }

            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ecf0f1, stop:1 #d5dbdb);
                border: 2px solid #bdc3c7;
                border-bottom: none;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                min-width: 120px;
                min-height: 35px;
                padding: 8px 16px;
                margin-right: 2px;
                font-weight: 600;
                font-size: 11px;
                color: #2c3e50;
            }

            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-color: #2980b9;
                font-weight: bold;
            }

            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-color: #95a5a6;
            }

            QTabBar::tab:first {
                margin-left: 0;
            }

            QTabBar::tab:last {
                margin-right: 0;
            }
        """)
        return tabs

    def setup_tabs(self):
        """Setup all tabs with enhanced organization"""
        # Create tabs with icons and better organization
        tab_configs = [
            ("🏠", "Dashboard", self.create_health_tab),
            ("🧹", "Cleaner", self.create_cleaner_tab),
            ("⚡", "Performance", self.create_performance_tab),
            ("🎮", "Gaming", self.create_gaming_tab),
            ("📊", "Processes", self.create_processes_tab),
            ("⏰", "Scheduler", self.create_scheduler_tab),
            ("🔧", "Startup", self.create_startup_tab),
            ("⚙️", "Settings", self.create_settings_tab)
        ]

        for icon, title, create_func in tab_configs:
            tab_widget = create_func()
            self.tabs.addTab(tab_widget, f"{icon} {title}")

    def create_primary_button(self, text, color="#3498db", icon=""):
        """Create a modern primary button with enhanced styling"""
        button = QPushButton(f"{icon} {text}" if icon else text)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 13px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.lighten_color(color)}, stop:1 {color});
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.darken_color(color)}, stop:1 {self.darken_color(self.darken_color(color))});
            }}
            QPushButton:disabled {{
                background: #bdc3c7;
                color: #7f8c8d;
            }}
        """)
        return button

    def create_secondary_button(self, text, color="#95a5a6", icon=""):
        """Create a modern secondary button with enhanced styling"""
        button = QPushButton(f"{icon} {text}" if icon else text)
        button.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                color: {color};
                border: 2px solid {color};
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 600;
                font-size: 12px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }}
            QPushButton:hover {{
                background: {color};
                color: white;
            }}
            QPushButton:pressed {{
                background: {self.darken_color(color)};
                border-color: {self.darken_color(color)};
                color: white;
            }}
            QPushButton:disabled {{
                border-color: #bdc3c7;
                color: #bdc3c7;
            }}
        """)
        return button

    def create_danger_button(self, text, color="#e74c3c", icon=""):
        """Create a modern danger button for destructive actions"""
        return self.create_primary_button(text, color, icon)

    def create_success_button(self, text, color="#27ae60", icon=""):
        """Create a modern success button for positive actions"""
        return self.create_primary_button(text, color, icon)

    def create_warning_button(self, text, color="#f39c12", icon=""):
        """Create a modern warning button for caution actions"""
        return self.create_primary_button(text, color, icon)

    def create_health_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # System Health Score Card
        health_card = self.create_health_score_card()

        # Main content area
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)

        # Left column - Recommendations
        left_column = QVBoxLayout()
        recommendations_group = QGroupBox("🔍 System Analysis & Recommendations")
        recommendations_layout = QVBoxLayout(recommendations_group)

        self.recommendations_label = QLabel("Analyzing system performance...")
        self.recommendations_label.setWordWrap(True)
        self.recommendations_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                line-height: 1.4;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #3498db;
            }
        """)
        recommendations_layout.addWidget(self.recommendations_label)

        left_column.addWidget(recommendations_group)

        # Right column - Quick Actions
        right_column = QVBoxLayout()
        actions_group = QGroupBox("⚡ Quick Actions")
        actions_layout = QVBoxLayout(actions_group)
        actions_layout.setSpacing(12)

        quick_clean_btn = self.create_primary_button("Quick System Clean", "#3498db", "🧹")
        quick_clean_btn.clicked.connect(self.quick_clean)

        refresh_health_btn = self.create_secondary_button("Refresh Analysis", "#f39c12", "🔄")
        refresh_health_btn.clicked.connect(self.calculate_health_score)

        full_optimize_btn = self.create_success_button("Full Optimization", "#27ae60", "⚡")
        full_optimize_btn.clicked.connect(self.run_optimization)

        actions_layout.addWidget(quick_clean_btn)
        actions_layout.addWidget(refresh_health_btn)
        actions_layout.addWidget(full_optimize_btn)
        actions_layout.addStretch()

        right_column.addWidget(actions_group)

        # Add columns to content layout
        content_layout.addLayout(left_column, 2)  # 2/3 width
        content_layout.addLayout(right_column, 1)  # 1/3 width

        layout.addWidget(health_card)
        layout.addLayout(content_layout)
        layout.addStretch()

        # Calculate initial health score
        QTimer.singleShot(1000, self.calculate_health_score)

        return tab

    def create_health_score_card(self):
        """Create an enhanced health score display card"""
        card = QWidget()
        card.setFixedHeight(120)
        card.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px solid #e9ecef;
                border-radius: 12px;
            }
        """)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(30, 20, 30, 20)
        layout.setSpacing(30)

        # Score display
        score_layout = QVBoxLayout()
        score_title = QLabel("System Health Score")
        score_title.setFont(QFont("Segoe UI", 12, QFont.Bold))
        score_title.setStyleSheet("color: #2c3e50; background: transparent;")

        self.health_score_label = QLabel("Calculating...")
        self.health_score_label.setFont(QFont("Segoe UI", 32, QFont.Bold))
        self.health_score_label.setAlignment(Qt.AlignCenter)
        self.health_score_label.setStyleSheet("color: #3498db; background: transparent;")

        score_layout.addWidget(score_title)
        score_layout.addWidget(self.health_score_label)

        # Progress visualization
        progress_layout = QVBoxLayout()
        progress_title = QLabel("Overall Performance")
        progress_title.setFont(QFont("Segoe UI", 12, QFont.Bold))
        progress_title.setStyleSheet("color: #2c3e50; background: transparent;")

        self.health_progress = QProgressBar()
        self.health_progress.setRange(0, 100)
        self.health_progress.setValue(0)
        self.health_progress.setTextVisible(False)
        self.health_progress.setFixedHeight(25)
        self.health_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                background-color: #ecf0f1;
                text-align: center;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
            }
        """)

        self.health_status_label = QLabel("Analyzing...")
        self.health_status_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.health_status_label.setAlignment(Qt.AlignCenter)
        self.health_status_label.setStyleSheet("color: #7f8c8d; background: transparent;")

        progress_layout.addWidget(progress_title)
        progress_layout.addWidget(self.health_progress)
        progress_layout.addWidget(self.health_status_label)

        layout.addLayout(score_layout, 1)
        layout.addLayout(progress_layout, 2)

        return card

    def calculate_health_score(self):
        """Calculate system health score based on various metrics"""
        try:
            score = 100
            recommendations = []

            # CPU usage check
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 80:
                score -= 15
                recommendations.append("• High CPU usage detected. Consider closing unnecessary programs.")
            elif cpu_percent > 60:
                score -= 8
                recommendations.append("• Moderate CPU usage. Monitor running processes.")

            # Memory usage check
            ram = psutil.virtual_memory()
            if ram.percent > 85:
                score -= 20
                recommendations.append("• High memory usage. Consider closing memory-intensive applications.")
            elif ram.percent > 70:
                score -= 10
                recommendations.append("• Moderate memory usage. Monitor RAM consumption.")

            # Disk usage check
            try:
                disk = psutil.disk_usage('C:')
                if disk.percent > 90:
                    score -= 25
                    recommendations.append("• Disk space critically low. Clean up files immediately.")
                elif disk.percent > 80:
                    score -= 15
                    recommendations.append("• Disk space running low. Consider cleaning temporary files.")
            except:
                pass

            # Process count check
            process_count = len(psutil.pids())
            if process_count > 200:
                score -= 10
                recommendations.append("• High number of running processes. Consider disabling startup programs.")

            # Temperature check (if available)
            try:
                temps = psutil.sensors_temperatures()
                if temps:
                    for name, entries in temps.items():
                        if 'cpu' in name.lower() or 'core' in name.lower():
                            if entries and entries[0].current > 80:
                                score -= 15
                                recommendations.append("• High CPU temperature detected. Check cooling system.")
                            break
            except:
                pass

            # Ensure score doesn't go below 0
            score = max(0, score)

            # Update UI with enhanced styling
            self.health_score_label.setText(f"{score}")
            self.health_progress.setValue(score)

            # Set color and status based on score
            if score >= 80:
                color = "#27ae60"  # Green
                status = "Excellent"
                status_icon = "✅"
            elif score >= 60:
                color = "#f39c12"  # Orange
                status = "Good"
                status_icon = "⚠️"
            elif score >= 40:
                color = "#e67e22"  # Red-Orange
                status = "Fair"
                status_icon = "🔶"
            else:
                color = "#e74c3c"  # Red
                status = "Poor"
                status_icon = "❌"

            # Update score label color
            self.health_score_label.setStyleSheet(f"color: {color}; background: transparent;")

            # Update progress bar with dynamic color
            self.health_progress.setStyleSheet(f"""
                QProgressBar {{
                    border: 2px solid #bdc3c7;
                    border-radius: 12px;
                    background-color: #ecf0f1;
                    text-align: center;
                }}
                QProgressBar::chunk {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {color}, stop:1 {self.darken_color(color)});
                    border-radius: 10px;
                }}
            """)

            # Update status label
            self.health_status_label.setText(f"{status_icon} {status}")
            self.health_status_label.setStyleSheet(f"color: {color}; background: transparent; font-weight: bold;")

            # Update recommendations with better formatting
            if not recommendations:
                recommendations.append("✅ System is running optimally!")
                recommendations.append("💡 Consider running scheduled maintenance to keep performance optimal.")

            recommendations_text = f"<h3 style='color: {color}; margin-bottom: 10px;'>{status_icon} System Status: {status}</h3>"
            recommendations_text += "<div style='line-height: 1.6;'>"
            for rec in recommendations:
                recommendations_text += f"<p style='margin: 8px 0;'>{rec}</p>"
            recommendations_text += "</div>"

            self.recommendations_label.setText(recommendations_text)
            self.recommendations_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 12px;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 6px;
                    border-left: 4px solid {color};
                }}
            """)

        except Exception as e:
            self.health_score_label.setText("Error")
            self.recommendations_label.setText(f"Error calculating health score: {str(e)}")

    def quick_clean(self):
        """Perform quick system cleanup"""
        try:
            # Run basic cleanup operations
            self.optimizer.clean_temp_files()
            self.optimizer.clean_browser_cache()

            QMessageBox.information(self, 'Success', 'Quick cleanup completed!')

            # Refresh health score
            QTimer.singleShot(1000, self.calculate_health_score)

        except Exception as e:
            QMessageBox.critical(self, 'Error', f'Quick cleanup failed: {str(e)}')

    def create_stat_widget(self, title, value):
        container = QGroupBox(title)
        container.setStyleSheet("QGroupBox { border: 1px solid gray; border-radius: 5px; margin-top: 1ex; }"
                                "QGroupBox::title { subcontrol-origin: margin; padding: 0 3px; }")
        layout = QVBoxLayout(container)
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 16, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        
        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(0)
        progress.setTextVisible(False)
        
        layout.addWidget(value_label)
        layout.addWidget(progress)
        
        return container
    
    def update_system_stats(self):
        # CPU usage
        cpu_percent = psutil.cpu_percent()
        self.cpu_card.value_label.setText(f"{cpu_percent:.1f}%")
        self.cpu_card.progress_bar.setValue(int(cpu_percent))

        # RAM usage
        ram = psutil.virtual_memory()
        ram_percent = ram.percent
        ram_gb = ram.used / (1024**3)
        total_gb = ram.total / (1024**3)
        self.ram_card.value_label.setText(f"{ram_gb:.1f}/{total_gb:.1f} GB")
        self.ram_card.progress_bar.setValue(int(ram_percent))

        # Disk usage (Windows C: drive)
        try:
            disk = psutil.disk_usage('C:')
            disk_percent = disk.percent
            disk_gb = disk.used / (1024**3)
            total_disk_gb = disk.total / (1024**3)
            self.disk_card.value_label.setText(f"{disk_gb:.0f}/{total_disk_gb:.0f} GB")
            self.disk_card.progress_bar.setValue(int(disk_percent))
        except:
            self.disk_card.value_label.setText("N/A")
            self.disk_card.progress_bar.setValue(0)

        # Network usage
        try:
            current_network = psutil.net_io_counters()
            bytes_sent = current_network.bytes_sent - self.last_network_stats.bytes_sent
            bytes_recv = current_network.bytes_recv - self.last_network_stats.bytes_recv
            total_bytes = bytes_sent + bytes_recv

            # Convert to KB/s (update interval is 1 second)
            speed_kbs = total_bytes / 1024
            if speed_kbs > 1024:
                speed_text = f"{speed_kbs/1024:.1f} MB/s"
                progress_val = min(int(speed_kbs / 1024), 100)  # Cap at 100 MB/s for progress bar
            else:
                speed_text = f"{speed_kbs:.0f} KB/s"
                progress_val = min(int(speed_kbs / 10), 100)  # Cap at 1 MB/s for progress bar

            self.network_card.value_label.setText(speed_text)
            self.network_card.progress_bar.setValue(progress_val)
            self.last_network_stats = current_network
        except:
            self.network_card.value_label.setText("N/A")
            self.network_card.progress_bar.setValue(0)

        # Temperature (if available)
        try:
            temps = psutil.sensors_temperatures()
            if temps:
                # Try to get CPU temperature
                cpu_temp = None
                for name, entries in temps.items():
                    if 'cpu' in name.lower() or 'core' in name.lower():
                        if entries:
                            cpu_temp = entries[0].current
                            break

                if cpu_temp:
                    self.temp_card.value_label.setText(f"{cpu_temp:.0f}°C")
                    # Temperature progress bar: 0-100°C scale
                    temp_progress = min(int(cpu_temp), 100)
                    self.temp_card.progress_bar.setValue(temp_progress)
                else:
                    self.temp_card.value_label.setText("N/A")
                    self.temp_card.progress_bar.setValue(0)
            else:
                self.temp_card.value_label.setText("N/A")
                self.temp_card.progress_bar.setValue(0)
        except:
            self.temp_card.value_label.setText("N/A")
            self.temp_card.progress_bar.setValue(0)

        # Update real-time indicator animation
        current_color = self.realtime_indicator.styleSheet()
        if "#27ae60" in current_color:
            self.realtime_indicator.setStyleSheet("color: #2ecc71; background: transparent;")
        else:
            self.realtime_indicator.setStyleSheet("color: #27ae60; background: transparent;")
    
    def tr(self, key):
        """Get translation for key"""
        return self.optimizer.tr(key)
    
    def update_title(self):
        """Update window title with translated app name"""
        self.setWindowTitle(self.tr("app_title"))
    
    def apply_theme(self):
        """Apply enhanced light/dark theme based on config"""
        theme = self.optimizer.config.get('theme', 'light')

        if theme == 'dark':
            self.apply_dark_theme()
        else:
            self.apply_light_theme()

    def apply_light_theme(self):
        """Apply modern light theme"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #2c3e50;
            }

            QWidget {
                background-color: transparent;
                color: #2c3e50;
                font-family: 'Segoe UI', Arial, sans-serif;
            }

            QGroupBox {
                font-weight: 600;
                font-size: 12px;
                color: #34495e;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #ffffff;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 4px 8px;
                background-color: #3498db;
                color: white;
                border-radius: 4px;
                font-weight: bold;
            }

            QLabel {
                color: #2c3e50;
                font-size: 11px;
            }

            QCheckBox {
                font-size: 11px;
                color: #2c3e50;
                spacing: 8px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #bdc3c7;
                background-color: #ffffff;
            }

            QCheckBox::indicator:checked {
                background-color: #3498db;
                border-color: #2980b9;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }

            QCheckBox::indicator:hover {
                border-color: #3498db;
            }

            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: #ffffff;
                margin-top: 10px;
            }

            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ecf0f1, stop:1 #d5dbdb);
                border: 2px solid #bdc3c7;
                border-bottom: none;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                min-width: 120px;
                min-height: 35px;
                padding: 8px 16px;
                margin-right: 2px;
                font-weight: 600;
                font-size: 11px;
                color: #2c3e50;
            }

            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-color: #2980b9;
                font-weight: bold;
            }

            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-color: #95a5a6;
            }

            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                background-color: #ecf0f1;
                text-align: center;
                color: #2c3e50;
            }

            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 4px;
            }
        """)

    def apply_dark_theme(self):
        """Apply enhanced modern dark theme"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a1a, stop:1 #2d2d2d);
                color: #ffffff;
            }

            QWidget {
                background-color: transparent;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }

            QGroupBox {
                font-weight: 600;
                font-size: 12px;
                color: #ffffff;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #2d2d2d;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 4px 8px;
                background-color: #e74c3c;
                color: white;
                border-radius: 4px;
                font-weight: bold;
            }

            QLabel {
                color: #ffffff;
                font-size: 11px;
            }

            QCheckBox {
                font-size: 11px;
                color: #ffffff;
                spacing: 8px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #666666;
                background-color: #1a1a1a;
            }

            QCheckBox::indicator:checked {
                background-color: #e74c3c;
                border-color: #c0392b;
            }

            QCheckBox::indicator:hover {
                border-color: #e74c3c;
            }

            QTabWidget::pane {
                border: 2px solid #555555;
                border-radius: 8px;
                background-color: #2d2d2d;
                margin-top: 10px;
            }

            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3d3d3d, stop:1 #2d2d2d);
                border: 2px solid #555555;
                border-bottom: none;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                min-width: 120px;
                min-height: 35px;
                padding: 8px 16px;
                margin-right: 2px;
                font-weight: 600;
                font-size: 11px;
                color: #ffffff;
            }

            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border-color: #c0392b;
                font-weight: bold;
            }

            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4d4d4d, stop:1 #3d3d3d);
                border-color: #666666;
            }

            QProgressBar {
                border: 2px solid #555555;
                border-radius: 6px;
                background-color: #1a1a1a;
                text-align: center;
                color: #ffffff;
            }

            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e74c3c, stop:1 #c0392b);
                border-radius: 4px;
            }
        """)

        # Update system monitoring header for dark theme
        if hasattr(self, 'cpu_card'):
            header_style = """
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #2d2d2d, stop:1 #3d3d3d);
                    border-radius: 12px;
                    margin: 8px;
                }
            """
            # Apply to header if it exists
            header = self.findChild(QWidget)
            if header:
                header.setStyleSheet(header_style)
    
    def on_config_changed(self):
        """Handle configuration changes"""
        self.apply_theme()
        self.update_title()
        self.retranslate_ui()
    
    def retranslate_ui(self):
        """Update all UI text with new translations"""
        # Header labels are now handled by the modern card design
        # No need to update individual labels as they use fixed English terms for clarity
        
        # Update button text
        self.optimize_btn.setText(self.tr("optimize_now"))
        
        # Update tab names
        self.tabs.setTabText(0, self.tr("system_health"))
        self.tabs.setTabText(1, self.tr("cleaner"))
        self.tabs.setTabText(2, self.tr("startup"))
        self.tabs.setTabText(3, self.tr("performance"))
        self.tabs.setTabText(4, self.tr("processes"))
        self.tabs.setTabText(5, self.tr("gaming_mode"))
        self.tabs.setTabText(6, self.tr("scheduler"))
        self.tabs.setTabText(7, self.tr("settings"))
        
        # Update settings tab
        self.language_label.setText(self.tr("language"))
        self.theme_label.setText(self.tr("theme"))
        self.clean_temp_check.setText(self.tr("clean_temp_files"))
        self.clean_cache_check.setText(self.tr("clean_browser_cache"))
        self.disable_startup_check.setText(self.tr("disable_startup"))
        self.disable_telemetry_check.setText(self.tr("disable_telemetry"))
        self.optimize_disk_check.setText(self.tr("optimize_disk"))
        self.detect_bloatware_check.setText(self.tr("detect_bloatware"))
    
    def create_cleaner_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Two-column layout
        main_layout = QHBoxLayout()
        main_layout.setSpacing(20)

        # Left column - Cleaning options
        left_column = QVBoxLayout()

        # Basic cleaning options
        basic_group = QGroupBox("🧹 Essential Cleaning")
        basic_layout = QVBoxLayout(basic_group)
        basic_layout.setSpacing(12)

        self.clean_temp_check = QCheckBox("🗂️ Clean Temporary Files")
        self.clean_temp_check.setChecked(True)

        self.clean_cache_check = QCheckBox("🌐 Clean Browser Cache")
        self.clean_cache_check.setChecked(True)

        self.clean_logs_check = QCheckBox("📋 Clean System Logs")
        self.clean_logs_check.setChecked(True)

        basic_layout.addWidget(self.clean_temp_check)
        basic_layout.addWidget(self.clean_cache_check)
        basic_layout.addWidget(self.clean_logs_check)

        # Advanced cleaning options
        advanced_group = QGroupBox("⚙️ Advanced Cleaning")
        advanced_layout = QVBoxLayout(advanced_group)
        advanced_layout.setSpacing(12)

        self.clean_registry_check = QCheckBox("🔧 Clean Invalid Registry Entries")
        self.clean_registry_check.setChecked(False)

        warning_label = QLabel("⚠️ Advanced options require careful consideration")
        warning_label.setStyleSheet("""
            QLabel {
                color: #e67e22;
                font-size: 10px;
                font-style: italic;
                padding: 5px;
                background-color: #fef9e7;
                border-radius: 4px;
                border-left: 3px solid #f39c12;
            }
        """)

        advanced_layout.addWidget(self.clean_registry_check)
        advanced_layout.addWidget(warning_label)

        left_column.addWidget(basic_group)
        left_column.addWidget(advanced_group)
        left_column.addStretch()

        # Right column - File analysis tools
        right_column = QVBoxLayout()

        # Duplicate files section
        duplicates_group = QGroupBox("📁 Duplicate File Scanner")
        duplicates_layout = QVBoxLayout(duplicates_group)
        duplicates_layout.setSpacing(12)

        find_duplicates_btn = self.create_primary_button("Scan for Duplicates", "#9b59b6", "🔍")
        find_duplicates_btn.clicked.connect(self.find_duplicates)

        self.duplicates_label = QLabel("Click 'Scan for Duplicates' to find identical files that are wasting space")
        self.duplicates_label.setWordWrap(True)
        self.duplicates_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 6px;
                font-size: 11px;
                color: #6c757d;
            }
        """)

        duplicates_layout.addWidget(find_duplicates_btn)
        duplicates_layout.addWidget(self.duplicates_label)

        # Large files section
        large_files_group = QGroupBox("📊 Large File Analyzer")
        large_files_layout = QVBoxLayout(large_files_group)
        large_files_layout.setSpacing(12)

        find_large_btn = self.create_primary_button("Find Large Files", "#e67e22", "📈")
        find_large_btn.clicked.connect(self.find_large_files)

        self.large_files_label = QLabel("Click 'Find Large Files' to identify files over 100MB that may be consuming unnecessary space")
        self.large_files_label.setWordWrap(True)
        self.large_files_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 6px;
                font-size: 11px;
                color: #6c757d;
            }
        """)

        large_files_layout.addWidget(find_large_btn)
        large_files_layout.addWidget(self.large_files_label)

        right_column.addWidget(duplicates_group)
        right_column.addWidget(large_files_group)
        right_column.addStretch()

        # Add columns to main layout
        main_layout.addLayout(left_column, 1)
        main_layout.addLayout(right_column, 1)

        layout.addLayout(main_layout)

        return tab

    def find_duplicates(self):
        """Find and display duplicate files"""
        try:
            self.duplicates_label.setText("Scanning for duplicate files...")
            duplicates = self.optimizer.find_duplicate_files()

            if duplicates:
                total_duplicates = sum(len(group) - 1 for group in duplicates)  # -1 because we keep one copy
                total_size = 0

                # Calculate total size of duplicates
                for group in duplicates:
                    for i, file_path in enumerate(group[1:], 1):  # Skip first file (keep it)
                        try:
                            total_size += os.path.getsize(file_path)
                        except:
                            pass

                size_mb = total_size / (1024 * 1024)

                message = f"Found {total_duplicates} duplicate files in {len(duplicates)} groups.\n"
                message += f"Potential space savings: {size_mb:.2f} MB\n\n"
                message += "First few duplicate groups:\n"

                for i, group in enumerate(duplicates[:3]):  # Show first 3 groups
                    message += f"\nGroup {i+1}:\n"
                    for file_path in group:
                        message += f"  • {file_path}\n"

                if len(duplicates) > 3:
                    message += f"\n... and {len(duplicates) - 3} more groups"

                self.duplicates_label.setText(message)

                # Ask if user wants to delete duplicates
                reply = QMessageBox.question(
                    self, 'Delete Duplicates?',
                    f'Found {total_duplicates} duplicate files. Delete them to free {size_mb:.2f} MB?',
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    deleted_count = 0
                    for group in duplicates:
                        for file_path in group[1:]:  # Keep first file, delete others
                            try:
                                os.remove(file_path)
                                deleted_count += 1
                            except:
                                pass

                    QMessageBox.information(self, 'Success', f'Deleted {deleted_count} duplicate files!')
                    self.duplicates_label.setText(f"Deleted {deleted_count} duplicate files, freed {size_mb:.2f} MB")
            else:
                self.duplicates_label.setText("No duplicate files found!")

        except Exception as e:
            self.duplicates_label.setText(f"Error scanning for duplicates: {str(e)}")

    def find_large_files(self):
        """Find and display large files"""
        try:
            self.large_files_label.setText("Scanning for large files...")
            large_files = self.optimizer.find_large_files()

            if large_files:
                total_size = sum(f['size_mb'] for f in large_files)

                message = f"Found {len(large_files)} large files (>100MB).\n"
                message += f"Total size: {total_size:.2f} MB\n\n"
                message += "Largest files:\n"

                for i, file_info in enumerate(large_files[:5]):  # Show top 5
                    message += f"  • {os.path.basename(file_info['path'])} - {file_info['size_mb']:.1f} MB\n"
                    message += f"    {file_info['path']}\n"

                if len(large_files) > 5:
                    message += f"\n... and {len(large_files) - 5} more files"

                self.large_files_label.setText(message)
            else:
                self.large_files_label.setText("No large files found!")

        except Exception as e:
            self.large_files_label.setText(f"Error scanning for large files: {str(e)}")
    
    def create_startup_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.disable_startup_check = QCheckBox(self.tr("disable_startup"))
        self.disable_startup_check.setChecked(True)
        
        layout.addWidget(self.disable_startup_check)
        layout.addStretch()
        
        return tab
    
    def create_performance_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.disable_telemetry_check = QCheckBox(self.tr("disable_telemetry"))
        self.disable_telemetry_check.setChecked(True)
        
        self.optimize_disk_check = QCheckBox(self.tr("optimize_disk"))
        self.optimize_disk_check.setChecked(True)
        
        self.detect_bloatware_check = QCheckBox(self.tr("detect_bloatware"))
        self.detect_bloatware_check.setChecked(True)
        
        layout.addWidget(self.disable_telemetry_check)
        layout.addWidget(self.optimize_disk_check)
        layout.addWidget(self.detect_bloatware_check)
        layout.addStretch()
        
        return tab

    def create_processes_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Control buttons
        button_layout = QHBoxLayout()
        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self.refresh_processes)
        end_process_btn = QPushButton("End Process")
        end_process_btn.clicked.connect(self.end_selected_process)

        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(end_process_btn)
        button_layout.addStretch()

        # Process table
        self.process_table = QTableWidget()
        self.process_table.setColumnCount(5)
        self.process_table.setHorizontalHeaderLabels(["PID", "Name", "CPU %", "Memory (MB)", "Status"])
        self.process_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.process_table.setAlternatingRowColors(True)

        # Set column widths
        header = self.process_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # PID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Name
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # CPU
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Memory
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Status

        layout.addLayout(button_layout)
        layout.addWidget(self.process_table)

        # Initialize process list
        self.refresh_processes()

        return tab

    def refresh_processes(self):
        """Refresh the process list"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'status']):
                try:
                    pinfo = proc.info
                    memory_mb = pinfo['memory_info'].rss / 1024 / 1024 if pinfo['memory_info'] else 0
                    processes.append({
                        'pid': pinfo['pid'],
                        'name': pinfo['name'] or 'N/A',
                        'cpu_percent': pinfo['cpu_percent'] or 0,
                        'memory_mb': memory_mb,
                        'status': pinfo['status'] or 'unknown'
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass

            # Sort by CPU usage (descending)
            processes.sort(key=lambda x: x['cpu_percent'], reverse=True)

            # Update table
            self.process_table.setRowCount(len(processes))
            for i, proc in enumerate(processes):
                self.process_table.setItem(i, 0, QTableWidgetItem(str(proc['pid'])))
                self.process_table.setItem(i, 1, QTableWidgetItem(proc['name']))
                self.process_table.setItem(i, 2, QTableWidgetItem(f"{proc['cpu_percent']:.1f}"))
                self.process_table.setItem(i, 3, QTableWidgetItem(f"{proc['memory_mb']:.1f}"))
                self.process_table.setItem(i, 4, QTableWidgetItem(proc['status']))

        except Exception as e:
            print(f"Error refreshing processes: {e}")

    def end_selected_process(self):
        """End the selected process"""
        current_row = self.process_table.currentRow()
        if current_row >= 0:
            pid_item = self.process_table.item(current_row, 0)
            name_item = self.process_table.item(current_row, 1)

            if pid_item and name_item:
                pid = int(pid_item.text())
                name = name_item.text()

                reply = QMessageBox.question(
                    self, 'Confirm',
                    f'Are you sure you want to end process "{name}" (PID: {pid})?',
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    try:
                        proc = psutil.Process(pid)
                        proc.terminate()
                        self.refresh_processes()
                        QMessageBox.information(self, 'Success', f'Process "{name}" terminated successfully.')
                    except psutil.NoSuchProcess:
                        QMessageBox.warning(self, 'Error', 'Process no longer exists.')
                        self.refresh_processes()
                    except psutil.AccessDenied:
                        QMessageBox.warning(self, 'Error', 'Access denied. Cannot terminate this process.')
                    except Exception as e:
                        QMessageBox.critical(self, 'Error', f'Failed to terminate process: {str(e)}')

    def create_gaming_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Gaming mode status card
        status_card = self.create_gaming_status_card()

        # Main content layout
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)

        # Left column - Controls
        left_column = QVBoxLayout()

        controls_group = QGroupBox("🎮 Gaming Mode Controls")
        controls_layout = QVBoxLayout(controls_group)
        controls_layout.setSpacing(15)

        self.enable_gaming_btn = self.create_success_button("Enable Gaming Mode", "#27ae60", "🚀")
        self.enable_gaming_btn.clicked.connect(self.toggle_gaming_mode)

        self.disable_gaming_btn = self.create_danger_button("Disable Gaming Mode", "#e74c3c", "🛑")
        self.disable_gaming_btn.clicked.connect(self.toggle_gaming_mode)
        self.disable_gaming_btn.setEnabled(False)

        controls_layout.addWidget(self.enable_gaming_btn)
        controls_layout.addWidget(self.disable_gaming_btn)

        left_column.addWidget(controls_group)
        left_column.addStretch()

        # Right column - Information
        right_column = QVBoxLayout()

        description_group = QGroupBox("ℹ️ How Gaming Mode Works")
        description_layout = QVBoxLayout(description_group)

        description_text = QLabel("""
<h3 style='color: #2c3e50; margin-bottom: 10px;'>🎯 Performance Optimizations:</h3>
<ul style='line-height: 1.6; margin-left: 20px;'>
<li><strong>🛑 Service Management:</strong> Stops non-essential Windows services</li>
<li><strong>⚡ Power Plan:</strong> Switches to high-performance power settings</li>
<li><strong>🧹 Process Cleanup:</strong> Reduces background processes</li>
<li><strong>🎮 Resource Priority:</strong> Optimizes system resources for games</li>
</ul>

<div style='background-color: #fff3cd; padding: 12px; border-radius: 6px; border-left: 4px solid #ffc107; margin-top: 15px;'>
<strong>⚠️ Important Note:</strong> Some system features may be temporarily disabled.
You can restore normal operation by disabling Gaming Mode when finished gaming.
</div>
        """)
        description_text.setWordWrap(True)
        description_text.setStyleSheet("""
            QLabel {
                font-size: 12px;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
            }
        """)
        description_layout.addWidget(description_text)

        right_column.addWidget(description_group)

        # Add columns to content layout
        content_layout.addLayout(left_column, 1)
        content_layout.addLayout(right_column, 2)

        layout.addWidget(status_card)
        layout.addLayout(content_layout)
        layout.addStretch()

        # Update gaming mode status
        self.update_gaming_status()

        return tab

    def create_gaming_status_card(self):
        """Create a gaming mode status card"""
        card = QWidget()
        card.setFixedHeight(100)
        card.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px solid #e9ecef;
                border-radius: 12px;
            }
        """)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(25, 20, 25, 20)
        layout.setSpacing(20)

        # Status icon and text
        status_layout = QVBoxLayout()

        status_title = QLabel("Gaming Mode Status")
        status_title.setFont(QFont("Segoe UI", 12, QFont.Bold))
        status_title.setStyleSheet("color: #2c3e50; background: transparent;")

        self.gaming_status_label = QLabel("🎮 Gaming Mode: Disabled")
        self.gaming_status_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        self.gaming_status_label.setStyleSheet("color: #e74c3c; background: transparent;")

        status_layout.addWidget(status_title)
        status_layout.addWidget(self.gaming_status_label)

        layout.addLayout(status_layout)
        layout.addStretch()

        return card

    def toggle_gaming_mode(self):
        """Toggle gaming mode on/off"""
        sender = self.sender()

        if sender == self.enable_gaming_btn:
            success = self.optimizer.enable_gaming_mode()
            if success:
                QMessageBox.information(self, 'Success', 'Gaming Mode enabled successfully!')
            else:
                QMessageBox.warning(self, 'Error', 'Failed to enable Gaming Mode.')
        else:
            success = self.optimizer.disable_gaming_mode()
            if success:
                QMessageBox.information(self, 'Success', 'Gaming Mode disabled successfully!')
            else:
                QMessageBox.warning(self, 'Error', 'Failed to disable Gaming Mode.')

        self.update_gaming_status()

    def update_gaming_status(self):
        """Update gaming mode status display with enhanced styling"""
        is_active = self.optimizer.get_gaming_mode_status()

        if is_active:
            self.gaming_status_label.setText("🚀 Gaming Mode: ACTIVE")
            self.gaming_status_label.setStyleSheet("color: #27ae60; background: transparent; font-weight: bold;")
            self.enable_gaming_btn.setEnabled(False)
            self.disable_gaming_btn.setEnabled(True)
        else:
            self.gaming_status_label.setText("🎮 Gaming Mode: Disabled")
            self.gaming_status_label.setStyleSheet("color: #e74c3c; background: transparent; font-weight: bold;")
            self.enable_gaming_btn.setEnabled(True)
            self.disable_gaming_btn.setEnabled(False)

    def create_scheduler_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Scheduler status
        status_group = QGroupBox("Automatic Optimization Schedule")
        status_layout = QVBoxLayout(status_group)

        self.scheduler_enabled_check = QCheckBox("Enable Automatic Optimization")
        self.scheduler_enabled_check.setChecked(self.optimizer.config.get('auto_optimize', False))
        self.scheduler_enabled_check.toggled.connect(self.toggle_scheduler)

        # Schedule frequency
        frequency_layout = QHBoxLayout()
        frequency_layout.addWidget(QLabel("Run optimization every:"))

        self.frequency_combo = QComboBox()
        self.frequency_combo.addItem("Daily", "daily")
        self.frequency_combo.addItem("Weekly", "weekly")
        self.frequency_combo.addItem("Monthly", "monthly")

        current_freq = self.optimizer.config.get('auto_optimize_frequency', 'weekly')
        for i in range(self.frequency_combo.count()):
            if self.frequency_combo.itemData(i) == current_freq:
                self.frequency_combo.setCurrentIndex(i)
                break

        self.frequency_combo.currentIndexChanged.connect(self.update_frequency)

        frequency_layout.addWidget(self.frequency_combo)
        frequency_layout.addStretch()

        # Last run info
        self.last_run_label = QLabel("Last automatic optimization: Never")
        last_run = self.optimizer.config.get('last_auto_optimize', None)
        if last_run:
            self.last_run_label.setText(f"Last automatic optimization: {last_run}")

        # Next run info
        self.next_run_label = QLabel("Next automatic optimization: Not scheduled")
        self.update_next_run_display()

        status_layout.addWidget(self.scheduler_enabled_check)
        status_layout.addLayout(frequency_layout)
        status_layout.addWidget(self.last_run_label)
        status_layout.addWidget(self.next_run_label)

        # Manual controls
        controls_group = QGroupBox("Manual Controls")
        controls_layout = QHBoxLayout(controls_group)

        run_now_btn = QPushButton("Run Optimization Now")
        run_now_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        run_now_btn.clicked.connect(self.run_scheduled_optimization)

        test_schedule_btn = QPushButton("Test Schedule")
        test_schedule_btn.clicked.connect(self.test_schedule)

        controls_layout.addWidget(run_now_btn)
        controls_layout.addWidget(test_schedule_btn)
        controls_layout.addStretch()

        # Options for scheduled optimization
        options_group = QGroupBox("Scheduled Optimization Options")
        options_layout = QVBoxLayout(options_group)

        self.schedule_clean_temp = QCheckBox("Clean temporary files")
        self.schedule_clean_temp.setChecked(True)

        self.schedule_clean_cache = QCheckBox("Clean browser cache")
        self.schedule_clean_cache.setChecked(True)

        self.schedule_clean_logs = QCheckBox("Clean system logs")
        self.schedule_clean_logs.setChecked(True)

        self.schedule_optimize_disk = QCheckBox("Optimize disk")
        self.schedule_optimize_disk.setChecked(True)

        options_layout.addWidget(self.schedule_clean_temp)
        options_layout.addWidget(self.schedule_clean_cache)
        options_layout.addWidget(self.schedule_clean_logs)
        options_layout.addWidget(self.schedule_optimize_disk)

        layout.addWidget(status_group)
        layout.addWidget(controls_group)
        layout.addWidget(options_group)
        layout.addStretch()

        return tab

    def toggle_scheduler(self, enabled):
        """Toggle automatic optimization scheduler"""
        self.optimizer.config['auto_optimize'] = enabled
        self.optimizer.save_config()
        self.update_next_run_display()

        if enabled:
            QMessageBox.information(self, 'Scheduler Enabled',
                                  'Automatic optimization has been enabled. The system will be optimized according to your schedule.')
        else:
            QMessageBox.information(self, 'Scheduler Disabled',
                                  'Automatic optimization has been disabled.')

    def update_frequency(self, index):
        """Update optimization frequency"""
        frequency = self.frequency_combo.currentData()
        self.optimizer.config['auto_optimize_frequency'] = frequency
        self.optimizer.save_config()
        self.update_next_run_display()

    def update_next_run_display(self):
        """Update the next run display"""
        if not self.optimizer.config.get('auto_optimize', False):
            self.next_run_label.setText("Next automatic optimization: Not scheduled")
            return

        frequency = self.optimizer.config.get('auto_optimize_frequency', 'weekly')
        last_run = self.optimizer.config.get('last_auto_optimize', None)

        if last_run:
            from datetime import datetime, timedelta
            try:
                last_date = datetime.fromisoformat(last_run)
                if frequency == 'daily':
                    next_date = last_date + timedelta(days=1)
                elif frequency == 'weekly':
                    next_date = last_date + timedelta(weeks=1)
                else:  # monthly
                    next_date = last_date + timedelta(days=30)

                self.next_run_label.setText(f"Next automatic optimization: {next_date.strftime('%Y-%m-%d %H:%M')}")
            except:
                self.next_run_label.setText("Next automatic optimization: Error calculating")
        else:
            self.next_run_label.setText("Next automatic optimization: Will run on next startup")

    def run_scheduled_optimization(self):
        """Run optimization with scheduled options"""
        try:
            results = []

            if self.schedule_clean_temp.isChecked():
                self.optimizer.clean_temp_files()
                results.append("Cleaned temporary files")

            if self.schedule_clean_cache.isChecked():
                self.optimizer.clean_browser_cache()
                results.append("Cleaned browser cache")

            if self.schedule_clean_logs.isChecked():
                freed_mb = self.optimizer.clean_system_logs()
                results.append(f"Cleaned system logs ({freed_mb:.1f} MB freed)")

            if self.schedule_optimize_disk.isChecked():
                self.optimizer.optimize_disk()
                results.append("Optimized disk performance")

            # Update last run time
            from datetime import datetime
            self.optimizer.config['last_auto_optimize'] = datetime.now().isoformat()
            self.optimizer.save_config()

            # Update displays
            self.last_run_label.setText(f"Last automatic optimization: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
            self.update_next_run_display()

            # Show results
            result_text = "Scheduled optimization completed!\n\n" + "\n".join(f"• {result}" for result in results)
            QMessageBox.information(self, 'Optimization Complete', result_text)

            # Refresh health score if available
            if hasattr(self, 'calculate_health_score'):
                QTimer.singleShot(2000, self.calculate_health_score)

        except Exception as e:
            QMessageBox.critical(self, 'Error', f'Scheduled optimization failed: {str(e)}')

    def test_schedule(self):
        """Test the scheduling system"""
        frequency = self.optimizer.config.get('auto_optimize_frequency', 'weekly')
        enabled = self.optimizer.config.get('auto_optimize', False)

        if enabled:
            QMessageBox.information(self, 'Schedule Test',
                                  f'Scheduler is enabled and set to run {frequency}.\n'
                                  f'The optimization will run automatically according to your schedule.')
        else:
            QMessageBox.warning(self, 'Schedule Test',
                               'Scheduler is currently disabled. Enable it to use automatic optimization.')

    def create_settings_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Language selection
        language_group = QGroupBox(self.tr("language"))
        language_layout = QVBoxLayout(language_group)
        self.language_combo = QComboBox()
        self.language_combo.addItem("English", "en")
        self.language_combo.addItem("العربية", "ar")
        self.language_combo.setCurrentText("English" if self.optimizer.config.get('language', 'en') == 'en' else "العربية")
        self.language_combo.currentIndexChanged.connect(self.change_language)
        language_layout.addWidget(self.language_combo)
        
        # Theme selection
        theme_group = QGroupBox(self.tr("theme"))
        theme_layout = QVBoxLayout(theme_group)
        self.theme_combo = QComboBox()
        self.theme_combo.addItem(self.tr("light"), "light")
        self.theme_combo.addItem(self.tr("dark"), "dark")
        self.theme_combo.setCurrentText(self.tr("light") if self.optimizer.config.get('theme', 'light') == 'light' else self.tr("dark"))
        self.theme_combo.currentIndexChanged.connect(self.change_theme)
        theme_layout.addWidget(self.theme_combo)
        
        layout.addWidget(language_group)
        layout.addWidget(theme_group)
        layout.addStretch()
        
        return tab
    
    def change_language(self, index):
        lang = self.language_combo.currentData()
        self.optimizer.config['language'] = lang
        self.optimizer.save_config()
        self.configChanged.emit()
    
    def change_theme(self, index):
        theme = self.theme_combo.currentData()
        self.optimizer.config['theme'] = theme
        self.optimizer.save_config()
        self.configChanged.emit()
    
    def run_optimization(self):
        # This will trigger the optimization process with selected options
        try:
            results = []

            # Basic cleaning
            if hasattr(self, 'clean_temp_check') and self.clean_temp_check.isChecked():
                self.optimizer.clean_temp_files()
                results.append("Cleaned temporary files")

            if hasattr(self, 'clean_cache_check') and self.clean_cache_check.isChecked():
                self.optimizer.clean_browser_cache()
                results.append("Cleaned browser cache")

            if hasattr(self, 'clean_logs_check') and self.clean_logs_check.isChecked():
                freed_mb = self.optimizer.clean_system_logs()
                results.append(f"Cleaned system logs ({freed_mb:.1f} MB freed)")

            # Advanced cleaning
            if hasattr(self, 'clean_registry_check') and self.clean_registry_check.isChecked():
                cleaned_entries = self.optimizer.clean_registry()
                results.append(f"Cleaned registry ({cleaned_entries} invalid entries removed)")

            # Performance optimizations
            if hasattr(self, 'disable_startup_check') and self.disable_startup_check.isChecked():
                self.optimizer.disable_startup_programs()
                results.append("Disabled unnecessary startup programs")

            if hasattr(self, 'disable_telemetry_check') and self.disable_telemetry_check.isChecked():
                self.optimizer.disable_telemetry()
                results.append("Disabled telemetry and tracking")

            if hasattr(self, 'optimize_disk_check') and self.optimize_disk_check.isChecked():
                self.optimizer.optimize_disk()
                results.append("Optimized disk performance")

            if hasattr(self, 'detect_bloatware_check') and self.detect_bloatware_check.isChecked():
                bloatware = self.optimizer.detect_bloatware()
                if bloatware:
                    self.optimizer.remove_bloatware(bloatware)
                    results.append(f"Removed {len(bloatware)} bloatware applications")

            # Show results
            if results:
                result_text = "Optimization completed successfully!\n\n" + "\n".join(f"• {result}" for result in results)
                QMessageBox.information(self, 'Optimization Complete', result_text)

                # Refresh health score if available
                if hasattr(self, 'calculate_health_score'):
                    QTimer.singleShot(2000, self.calculate_health_score)
            else:
                QMessageBox.information(self, 'No Actions', 'No optimization options were selected.')

        except Exception as e:
            QMessageBox.critical(self, 'Error', f'Optimization failed: {str(e)}')