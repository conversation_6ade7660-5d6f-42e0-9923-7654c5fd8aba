import os
import shutil
import ctypes
import winreg
import subprocess
import json
import psutil
from datetime import datetime
import logging
import glob
import hashlib
from collections import defaultdict

# Setup logging
logging.basicConfig(
    filename='widdx_speedup.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SystemOptimizer:
    def __init__(self):
        self.config = self.load_config()
        self.is_ssd = self.check_disk_type()
        self.translations = self.load_translations()
        self.gaming_mode_active = False
        self.stopped_services = []
        
    def load_config(self):
        config_path = 'config.json'
        default_config = {
            'language': 'en',
            'theme': 'light',
            'clean_temp': True,
            'clean_logs': True,
            'clean_recycle_bin': True,
            'disable_telemetry': True,
            'auto_trim': True,
            'excluded_apps': []
        }
        
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"Error loading config: {e}")
                return default_config
        return default_config

    def load_translations(self):
        """Load translation files from translations directory"""
        translations = {}
        try:
            for file in glob.glob("translations/*.json"):
                lang = os.path.splitext(os.path.basename(file))[0]
                with open(file, 'r', encoding='utf-8') as f:
                    translations[lang] = json.load(f)
            return translations
        except Exception as e:
            logging.error(f"Error loading translations: {e}")
            return {}
    
    def save_config(self):
        try:
            with open('config.json', 'w') as f:
                json.dump(self.config, f, indent=4)
            return True
        except Exception as e:
            logging.error(f"Error saving config: {e}")
            return False

    def tr(self, key):
        """Translate key using current language"""
        lang = self.config.get('language', 'en')
        translations = self.translations.get(lang, {})
        return translations.get(key, key)
            
    def check_disk_type(self):
        """Check if system drive is SSD"""
        try:
            drive = os.environ['SystemDrive']
            result = subprocess.check_output(
                f'powershell "Get-PhysicalDisk | Where-Object {{$_.DeviceId -eq 0}} | Select-Object MediaType"',
                shell=True
            ).decode().strip()
            return 'SSD' in result
        except:
            return False
    
    def create_restore_point(self):
        """Create system restore point"""
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", "powershell",
                "Checkpoint-Computer -Description 'WIDDX Speed Up Restore Point'",
                None, 1
            )
            logging.info("System restore point created")
            return True
        except Exception as e:
            logging.error(f"Error creating restore point: {e}")
            return False
    
    def clean_temp_files(self):
        """Clean temporary files"""
        try:
            # Windows temp folders
            temp_dirs = [
                os.environ.get('TEMP', ''),
                os.environ.get('TMP', ''),
                r'C:\Windows\Temp',
                os.path.join(os.environ['LOCALAPPDATA'], 'Temp')
            ]
            
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    for filename in os.listdir(temp_dir):
                        file_path = os.path.join(temp_dir, filename)
                        try:
                            if os.path.isfile(file_path):
                                os.unlink(file_path)
                            elif os.path.isdir(file_path):
                                shutil.rmtree(file_path)
                        except Exception as e:
                            logging.warning(f"Could not delete {file_path}: {e}")
            
            logging.info("Temporary files cleaned")
            return True
        except Exception as e:
            logging.error(f"Error cleaning temp files: {e}")
            return False
    
    def clean_browser_cache(self):
        """Clean browser cache for common browsers"""
        browsers = ['Chrome', 'Firefox', 'Edge', 'Opera']
        appdata = os.environ['LOCALAPPDATA']
        
        for browser in browsers:
            cache_path = os.path.join(appdata, browser, 'User Data', 'Default', 'Cache')
            if os.path.exists(cache_path):
                try:
                    shutil.rmtree(cache_path)
                    logging.info(f"{browser} cache cleaned")
                except Exception as e:
                    logging.warning(f"Could not clean {browser} cache: {e}")
        return True
    
    def disable_startup_programs(self):
        """Disable unnecessary startup programs"""
        try:
            # Disable from registry
            key_paths = [
                r"Software\Microsoft\Windows\CurrentVersion\Run",
                r"Software\Microsoft\Windows\CurrentVersion\RunOnce",
                r"Software\Microsoft\Windows\CurrentVersion\RunServices"
            ]
            
            for path in key_paths:
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, path, 0, winreg.KEY_WRITE)
                    winreg.DeleteValue(key, "OneDrive")  # Example - disable OneDrive
                    winreg.CloseKey(key)
                except:
                    pass
            
            # Disable from startup folder
            startup_folder = os.path.join(os.environ['APPDATA'], 'Microsoft', 'Windows', 'Start Menu', 'Programs', 'Startup')
            for filename in os.listdir(startup_folder):
                if filename.endswith('.lnk'):
                    os.remove(os.path.join(startup_folder, filename))
            
            logging.info("Startup programs disabled")
            return True
        except Exception as e:
            logging.error(f"Error disabling startup programs: {e}")
            return False
    
    def disable_telemetry(self):
        """Disable Windows telemetry and tracking"""
        try:
            # Disable via registry
            telemetry_keys = [
                r"Software\Microsoft\Windows\CurrentVersion\Policies\DataCollection",
                r"Software\Policies\Microsoft\Windows\DataCollection",
                r"Software\Microsoft\Windows\CurrentVersion\DeliveryOptimization\Config"
            ]
            
            for key_path in telemetry_keys:
                try:
                    key = winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                    winreg.SetValueEx(key, "AllowTelemetry", 0, winreg.REG_DWORD, 0)
                    winreg.CloseKey(key)
                except:
                    pass
            
            # Disable services
            services = ["DiagTrack", "dmwappushservice"]
            for service in services:
                subprocess.run(f'sc stop "{service}"', shell=True)
                subprocess.run(f'sc config "{service}" start=disabled', shell=True)
            
            logging.info("Telemetry disabled")
            return True
        except Exception as e:
            logging.error(f"Error disabling telemetry: {e}")
            return False
    
    def optimize_disk(self):
        """Optimize disk based on type (SSD TRIM or HDD defrag)"""
        try:
            if self.is_ssd:
                # Run TRIM optimization
                result = subprocess.run('defrag /C /H /O', shell=True, capture_output=True, text=True)
                logging.info(f"SSD TRIM executed: {result.stdout}")
            else:
                # Run HDD defragmentation
                result = subprocess.run('defrag /C /H', shell=True, capture_output=True, text=True)
                logging.info(f"HDD defragmentation executed: {result.stdout}")
            return True
        except Exception as e:
            logging.error(f"Error optimizing disk: {e}")
            return False
    
    def detect_bloatware(self):
        """Detect common bloatware applications"""
        bloatware = [
            "Microsoft.BingNews", "Microsoft.BingWeather", "Microsoft.GetHelp",
            "Microsoft.Getstarted", "Microsoft.MicrosoftOfficeHub", "Microsoft.MicrosoftSolitaireCollection",
            "Microsoft.People", "Microsoft.SkypeApp", "Microsoft.WindowsAlarms",
            "Microsoft.WindowsCamera", "Microsoft.windowscommunicationsapps", "Microsoft.WindowsFeedbackHub",
            "Microsoft.WindowsMaps", "Microsoft.WindowsSoundRecorder", "Microsoft.Xbox.TCUI",
            "Microsoft.XboxApp", "Microsoft.XboxGameOverlay", "Microsoft.XboxGamingOverlay",
            "Microsoft.XboxIdentityProvider", "Microsoft.XboxSpeechToTextOverlay", "Microsoft.YourPhone",
            "Microsoft.ZuneMusic", "Microsoft.ZuneVideo"
        ]
        
        installed_bloat = []
        try:
            result = subprocess.check_output('powershell "Get-AppxPackage | Select-Object Name"', shell=True)
            installed_apps = result.decode().splitlines()
            
            for app in bloatware:
                if any(app in line for line in installed_apps):
                    installed_bloat.append(app)
        
        except Exception as e:
            logging.error(f"Error detecting bloatware: {e}")
        
        return installed_bloat
    
    def remove_bloatware(self, apps):
        """Remove specified bloatware apps"""
        try:
            for app in apps:
                subprocess.run(f'powershell "Remove-AppxPackage {app}"', shell=True)
            logging.info(f"Removed {len(apps)} bloatware apps")
            return True
        except Exception as e:
            logging.error(f"Error removing bloatware: {e}")
            return False
    
    def run_full_optimization(self):
        """Run complete optimization process"""
        try:
            # Create restore point first
            self.create_restore_point()
            
            # Run optimizations
            self.clean_temp_files()
            self.clean_browser_cache()
            self.disable_startup_programs()
            self.disable_telemetry()
            self.optimize_disk()
            
            # Detect and remove bloatware
            bloatware = self.detect_bloatware()
            if bloatware:
                self.remove_bloatware(bloatware)
            
            logging.info("Full optimization completed")
            return True
        except Exception as e:
            logging.error(f"Optimization failed: {e}")
            return False

    def enable_gaming_mode(self):
        """Enable gaming mode - stop non-essential services and processes"""
        if self.gaming_mode_active:
            return True

        try:
            # Services to stop for gaming mode
            non_essential_services = [
                "Spooler",  # Print Spooler
                "Fax",      # Fax service
                "WSearch",  # Windows Search
                "SysMain",  # Superfetch/Prefetch
                "Themes",   # Themes service
                "TabletInputService",  # Tablet PC Input Service
                "WerSvc",   # Windows Error Reporting
                "DiagTrack",  # Diagnostics Tracking Service
                "dmwappushservice",  # WAP Push Message Routing Service
            ]

            self.stopped_services = []
            for service in non_essential_services:
                try:
                    # Check if service is running
                    result = subprocess.run(f'sc query "{service}"', shell=True, capture_output=True, text=True)
                    if "RUNNING" in result.stdout:
                        # Stop the service
                        stop_result = subprocess.run(f'sc stop "{service}"', shell=True, capture_output=True, text=True)
                        if stop_result.returncode == 0:
                            self.stopped_services.append(service)
                            logging.info(f"Stopped service: {service}")
                except Exception as e:
                    logging.warning(f"Could not stop service {service}: {e}")

            # Set high performance power plan
            try:
                subprocess.run('powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c', shell=True)
                logging.info("Set high performance power plan")
            except Exception as e:
                logging.warning(f"Could not set power plan: {e}")

            # Disable Windows Game Mode (paradoxically can help in some cases)
            try:
                key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\GameBar")
                winreg.SetValueEx(key, "AutoGameModeEnabled", 0, winreg.REG_DWORD, 0)
                winreg.CloseKey(key)
            except Exception as e:
                logging.warning(f"Could not modify game bar settings: {e}")

            self.gaming_mode_active = True
            logging.info("Gaming mode enabled")
            return True

        except Exception as e:
            logging.error(f"Error enabling gaming mode: {e}")
            return False

    def disable_gaming_mode(self):
        """Disable gaming mode - restore stopped services"""
        if not self.gaming_mode_active:
            return True

        try:
            # Restart stopped services
            for service in self.stopped_services:
                try:
                    subprocess.run(f'sc start "{service}"', shell=True, capture_output=True, text=True)
                    logging.info(f"Restarted service: {service}")
                except Exception as e:
                    logging.warning(f"Could not restart service {service}: {e}")

            # Reset to balanced power plan
            try:
                subprocess.run('powercfg /setactive 381b4222-f694-41f0-9685-ff5bb260df2e', shell=True)
                logging.info("Reset to balanced power plan")
            except Exception as e:
                logging.warning(f"Could not reset power plan: {e}")

            self.stopped_services = []
            self.gaming_mode_active = False
            logging.info("Gaming mode disabled")
            return True

        except Exception as e:
            logging.error(f"Error disabling gaming mode: {e}")
            return False

    def get_gaming_mode_status(self):
        """Get current gaming mode status"""
        return self.gaming_mode_active

    def clean_registry(self):
        """Clean invalid registry entries"""
        try:
            cleaned_entries = 0

            # Registry keys to clean
            registry_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
                (winreg.HKEY_CURRENT_USER, r"Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\MuiCache"),
            ]

            for hkey, path in registry_paths:
                try:
                    key = winreg.OpenKey(hkey, path, 0, winreg.KEY_READ | winreg.KEY_WRITE)
                    i = 0
                    while True:
                        try:
                            name, value, _ = winreg.EnumValue(key, i)
                            # Check if the referenced file exists (for executable entries)
                            if isinstance(value, str) and (value.endswith('.exe') or '\\' in value):
                                # Extract file path
                                file_path = value.split('"')[1] if '"' in value else value.split()[0]
                                if not os.path.exists(file_path):
                                    try:
                                        winreg.DeleteValue(key, name)
                                        cleaned_entries += 1
                                        logging.info(f"Removed invalid registry entry: {name}")
                                        continue  # Don't increment i since we deleted an entry
                                    except:
                                        pass
                            i += 1
                        except OSError:
                            break
                    winreg.CloseKey(key)
                except Exception as e:
                    logging.warning(f"Could not clean registry path {path}: {e}")

            logging.info(f"Registry cleanup completed. Removed {cleaned_entries} invalid entries.")
            return cleaned_entries

        except Exception as e:
            logging.error(f"Error cleaning registry: {e}")
            return 0

    def find_duplicate_files(self, directories=None, min_size=1024):
        """Find duplicate files based on content hash"""
        if directories is None:
            directories = [
                os.path.join(os.environ['USERPROFILE'], 'Downloads'),
                os.path.join(os.environ['USERPROFILE'], 'Documents'),
                os.path.join(os.environ['USERPROFILE'], 'Pictures'),
                os.path.join(os.environ['USERPROFILE'], 'Videos'),
            ]

        try:
            file_hashes = defaultdict(list)
            duplicates = []

            for directory in directories:
                if not os.path.exists(directory):
                    continue

                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            # Skip small files
                            if os.path.getsize(file_path) < min_size:
                                continue

                            # Calculate file hash
                            hash_md5 = hashlib.md5()
                            with open(file_path, "rb") as f:
                                for chunk in iter(lambda: f.read(4096), b""):
                                    hash_md5.update(chunk)

                            file_hash = hash_md5.hexdigest()
                            file_hashes[file_hash].append(file_path)

                        except (OSError, IOError):
                            continue

            # Find duplicates
            for file_hash, file_list in file_hashes.items():
                if len(file_list) > 1:
                    duplicates.append(file_list)

            logging.info(f"Found {len(duplicates)} sets of duplicate files")
            return duplicates

        except Exception as e:
            logging.error(f"Error finding duplicate files: {e}")
            return []

    def find_large_files(self, min_size_mb=100, directories=None):
        """Find large files that might be taking up unnecessary space"""
        if directories is None:
            directories = [
                os.environ['USERPROFILE'],
                'C:\\Windows\\Temp',
                'C:\\Temp',
            ]

        try:
            large_files = []
            min_size_bytes = min_size_mb * 1024 * 1024

            for directory in directories:
                if not os.path.exists(directory):
                    continue

                for root, dirs, files in os.walk(directory):
                    # Skip system directories
                    if any(skip in root.lower() for skip in ['system32', 'syswow64', 'program files']):
                        continue

                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            size = os.path.getsize(file_path)
                            if size >= min_size_bytes:
                                large_files.append({
                                    'path': file_path,
                                    'size_mb': size / (1024 * 1024),
                                    'modified': datetime.fromtimestamp(os.path.getmtime(file_path))
                                })
                        except (OSError, IOError):
                            continue

            # Sort by size (largest first)
            large_files.sort(key=lambda x: x['size_mb'], reverse=True)

            logging.info(f"Found {len(large_files)} large files (>{min_size_mb}MB)")
            return large_files

        except Exception as e:
            logging.error(f"Error finding large files: {e}")
            return []

    def clean_system_logs(self):
        """Clean Windows system logs"""
        try:
            cleaned_size = 0
            log_paths = [
                'C:\\Windows\\Logs',
                'C:\\Windows\\System32\\LogFiles',
                'C:\\Windows\\Temp',
                os.path.join(os.environ['LOCALAPPDATA'], 'Microsoft\\Windows\\INetCache'),
            ]

            for log_path in log_paths:
                if os.path.exists(log_path):
                    for root, dirs, files in os.walk(log_path):
                        for file in files:
                            if file.endswith(('.log', '.tmp', '.dmp')):
                                file_path = os.path.join(root, file)
                                try:
                                    size = os.path.getsize(file_path)
                                    os.remove(file_path)
                                    cleaned_size += size
                                except (OSError, IOError):
                                    continue

            # Clear Windows Event Logs (requires admin)
            try:
                event_logs = ['Application', 'System', 'Security']
                for log in event_logs:
                    subprocess.run(f'wevtutil cl {log}', shell=True, capture_output=True)
            except:
                pass

            cleaned_mb = cleaned_size / (1024 * 1024)
            logging.info(f"System logs cleanup completed. Freed {cleaned_mb:.2f} MB")
            return cleaned_mb

        except Exception as e:
            logging.error(f"Error cleaning system logs: {e}")
            return 0