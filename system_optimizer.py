import os
import shutil
import ctypes
import winreg
import subprocess
import json
import psutil
from datetime import datetime
import logging
import glob

# Setup logging
logging.basicConfig(
    filename='winoptimizer.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SystemOptimizer:
    def __init__(self):
        self.config = self.load_config()
        self.is_ssd = self.check_disk_type()
        self.translations = self.load_translations()
        
    def load_config(self):
        config_path = 'config.json'
        default_config = {
            'language': 'en',
            'theme': 'light',
            'clean_temp': True,
            'clean_logs': True,
            'clean_recycle_bin': True,
            'disable_telemetry': True,
            'auto_trim': True,
            'excluded_apps': []
        }
        
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"Error loading config: {e}")
                return default_config
        return default_config

    def load_translations(self):
        """Load translation files from translations directory"""
        translations = {}
        try:
            for file in glob.glob("translations/*.json"):
                lang = os.path.splitext(os.path.basename(file))[0]
                with open(file, 'r', encoding='utf-8') as f:
                    translations[lang] = json.load(f)
            return translations
        except Exception as e:
            logging.error(f"Error loading translations: {e}")
            return {}
    
    def save_config(self):
        try:
            with open('config.json', 'w') as f:
                json.dump(self.config, f, indent=4)
            return True
        except Exception as e:
            logging.error(f"Error saving config: {e}")
            return False

    def tr(self, key):
        """Translate key using current language"""
        lang = self.config.get('language', 'en')
        translations = self.translations.get(lang, {})
        return translations.get(key, key)
            
    def check_disk_type(self):
        """Check if system drive is SSD"""
        try:
            drive = os.environ['SystemDrive']
            result = subprocess.check_output(
                f'powershell "Get-PhysicalDisk | Where-Object {{$_.DeviceId -eq 0}} | Select-Object MediaType"',
                shell=True
            ).decode().strip()
            return 'SSD' in result
        except:
            return False
    
    def create_restore_point(self):
        """Create system restore point"""
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", "powershell", 
                "Checkpoint-Computer -Description 'WinOptimizer Restore Point'", 
                None, 1
            )
            logging.info("System restore point created")
            return True
        except Exception as e:
            logging.error(f"Error creating restore point: {e}")
            return False
    
    def clean_temp_files(self):
        """Clean temporary files"""
        try:
            # Windows temp folders
            temp_dirs = [
                os.environ.get('TEMP', ''),
                os.environ.get('TMP', ''),
                r'C:\Windows\Temp',
                os.path.join(os.environ['LOCALAPPDATA'], 'Temp')
            ]
            
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    for filename in os.listdir(temp_dir):
                        file_path = os.path.join(temp_dir, filename)
                        try:
                            if os.path.isfile(file_path):
                                os.unlink(file_path)
                            elif os.path.isdir(file_path):
                                shutil.rmtree(file_path)
                        except Exception as e:
                            logging.warning(f"Could not delete {file_path}: {e}")
            
            logging.info("Temporary files cleaned")
            return True
        except Exception as e:
            logging.error(f"Error cleaning temp files: {e}")
            return False
    
    def clean_browser_cache(self):
        """Clean browser cache for common browsers"""
        browsers = ['Chrome', 'Firefox', 'Edge', 'Opera']
        appdata = os.environ['LOCALAPPDATA']
        
        for browser in browsers:
            cache_path = os.path.join(appdata, browser, 'User Data', 'Default', 'Cache')
            if os.path.exists(cache_path):
                try:
                    shutil.rmtree(cache_path)
                    logging.info(f"{browser} cache cleaned")
                except Exception as e:
                    logging.warning(f"Could not clean {browser} cache: {e}")
        return True
    
    def disable_startup_programs(self):
        """Disable unnecessary startup programs"""
        try:
            # Disable from registry
            key_paths = [
                r"Software\Microsoft\Windows\CurrentVersion\Run",
                r"Software\Microsoft\Windows\CurrentVersion\RunOnce",
                r"Software\Microsoft\Windows\CurrentVersion\RunServices"
            ]
            
            for path in key_paths:
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, path, 0, winreg.KEY_WRITE)
                    winreg.DeleteValue(key, "OneDrive")  # Example - disable OneDrive
                    winreg.CloseKey(key)
                except:
                    pass
            
            # Disable from startup folder
            startup_folder = os.path.join(os.environ['APPDATA'], 'Microsoft', 'Windows', 'Start Menu', 'Programs', 'Startup')
            for filename in os.listdir(startup_folder):
                if filename.endswith('.lnk'):
                    os.remove(os.path.join(startup_folder, filename))
            
            logging.info("Startup programs disabled")
            return True
        except Exception as e:
            logging.error(f"Error disabling startup programs: {e}")
            return False
    
    def disable_telemetry(self):
        """Disable Windows telemetry and tracking"""
        try:
            # Disable via registry
            telemetry_keys = [
                r"Software\Microsoft\Windows\CurrentVersion\Policies\DataCollection",
                r"Software\Policies\Microsoft\Windows\DataCollection",
                r"Software\Microsoft\Windows\CurrentVersion\DeliveryOptimization\Config"
            ]
            
            for key_path in telemetry_keys:
                try:
                    key = winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                    winreg.SetValueEx(key, "AllowTelemetry", 0, winreg.REG_DWORD, 0)
                    winreg.CloseKey(key)
                except:
                    pass
            
            # Disable services
            services = ["DiagTrack", "dmwappushservice"]
            for service in services:
                subprocess.run(f'sc stop "{service}"', shell=True)
                subprocess.run(f'sc config "{service}" start=disabled', shell=True)
            
            logging.info("Telemetry disabled")
            return True
        except Exception as e:
            logging.error(f"Error disabling telemetry: {e}")
            return False
    
    def optimize_disk(self):
        """Optimize disk based on type (SSD TRIM or HDD defrag)"""
        try:
            if self.is_ssd:
                # Run TRIM optimization
                result = subprocess.run('defrag /C /H /O', shell=True, capture_output=True, text=True)
                logging.info(f"SSD TRIM executed: {result.stdout}")
            else:
                # Run HDD defragmentation
                result = subprocess.run('defrag /C /H', shell=True, capture_output=True, text=True)
                logging.info(f"HDD defragmentation executed: {result.stdout}")
            return True
        except Exception as e:
            logging.error(f"Error optimizing disk: {e}")
            return False
    
    def detect_bloatware(self):
        """Detect common bloatware applications"""
        bloatware = [
            "Microsoft.BingNews", "Microsoft.BingWeather", "Microsoft.GetHelp",
            "Microsoft.Getstarted", "Microsoft.MicrosoftOfficeHub", "Microsoft.MicrosoftSolitaireCollection",
            "Microsoft.People", "Microsoft.SkypeApp", "Microsoft.WindowsAlarms",
            "Microsoft.WindowsCamera", "Microsoft.windowscommunicationsapps", "Microsoft.WindowsFeedbackHub",
            "Microsoft.WindowsMaps", "Microsoft.WindowsSoundRecorder", "Microsoft.Xbox.TCUI",
            "Microsoft.XboxApp", "Microsoft.XboxGameOverlay", "Microsoft.XboxGamingOverlay",
            "Microsoft.XboxIdentityProvider", "Microsoft.XboxSpeechToTextOverlay", "Microsoft.YourPhone",
            "Microsoft.ZuneMusic", "Microsoft.ZuneVideo"
        ]
        
        installed_bloat = []
        try:
            result = subprocess.check_output('powershell "Get-AppxPackage | Select-Object Name"', shell=True)
            installed_apps = result.decode().splitlines()
            
            for app in bloatware:
                if any(app in line for line in installed_apps):
                    installed_bloat.append(app)
        
        except Exception as e:
            logging.error(f"Error detecting bloatware: {e}")
        
        return installed_bloat
    
    def remove_bloatware(self, apps):
        """Remove specified bloatware apps"""
        try:
            for app in apps:
                subprocess.run(f'powershell "Remove-AppxPackage {app}"', shell=True)
            logging.info(f"Removed {len(apps)} bloatware apps")
            return True
        except Exception as e:
            logging.error(f"Error removing bloatware: {e}")
            return False
    
    def run_full_optimization(self):
        """Run complete optimization process"""
        try:
            # Create restore point first
            self.create_restore_point()
            
            # Run optimizations
            self.clean_temp_files()
            self.clean_browser_cache()
            self.disable_startup_programs()
            self.disable_telemetry()
            self.optimize_disk()
            
            # Detect and remove bloatware
            bloatware = self.detect_bloatware()
            if bloatware:
                self.remove_bloatware(bloatware)
            
            logging.info("Full optimization completed")
            return True
        except Exception as e:
            logging.error(f"Optimization failed: {e}")
            return False